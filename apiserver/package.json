{"name": "@tapdata/moa", "version": "1.0.1", "description": "Open API", "keywords": ["API Server"], "bin": {"moa": "bin/start.js"}, "engines": {"node": ">=8.9"}, "scripts": {"build:apidocs": "lb-apidocs", "build": "lb-tsc es2017 --outDir dist", "build:watch": "lb-tsc --watch", "clean:dist": "lb-clean dist", "clean:api": "lb-clean src/controllers/*.ts && lb-clean src/repositories/*.ts && lb-clean src/models/*.ts && lb-clean src/datasources/*.ts", "clean": "lb-clean dist && npm run clean:api && lb-clean logs && lb-clean *.pid && lb-clean cache", "lint:fix": "npm run tslint:fix && npm run prettier:fix", "prettier:fix": "npm run prettier:cli -- --write", "tslint": "lb-tslint", "tslint:fix": "npm run tslint -- --fix", "pretest": "npm run clean && npm run build", "test": "echo \"no test specified.\"", "test:dev": "lb-mocha --allow-console-logs dist/test/**/*.js", "prestart": "npm run build", "start": "node .", "dev": "node .", "prepublishOnly": "npm run test", "run": "node app.js"}, "homepage": "https://github.com/tapd8/apig/tree/moa", "repository": {"type": "git", "url": "https://github.com/tapd8/apig/tree/moa"}, "author": "", "license": "", "dependencies": {"@loopback/authentication": "^1.0.6", "@loopback/boot": "^1.0.3", "@loopback/context": "^1.0.1", "@loopback/core": "^1.0.1", "@loopback/http-server": "latest", "@loopback/openapi-v3": "^1.1.0", "@loopback/repository": "^1.0.3", "@loopback/rest": "^1.2.0", "@loopback/service-proxy": "^1.0.1", "@types/mime": "^2.0.3", "@types/mongodb": "^3.1.22", "@types/socket.io": "^2.1.4", "change-case": "^3.0.2", "conf": "^5.0.0", "cross-spawn": "^6.0.5", "download-file": "^0.1.5", "eureka-nodejs-client": "^1.0.7", "exceljs": "^3.5.0", "express": "latest", "express-graphql": "0.12.0", "express-ws": "^4.0.0", "hashcode": "^1.0.3", "jsonwebtoken": "^8.4.0", "lodash": "^4.17.11", "log4js": "^3.0.6", "loopback-connector-mongodb": "~5.4.0", "loopback-connector-mssql": "^3.8.0", "loopback-connector-mysql": "^6.0.0", "loopback-connector-oracle": "^4.5.2", "loopback-connector-postgresql": "^5.3.0", "loopback-datasource-juggler": "^4.26.0", "make-dir": "^1.3.0", "mem-fs": "~1.1.3", "mem-fs-editor": "^5.1.0", "moment": "^2.24.0", "mongodb": "3.1.13", "mongodb-schema": "^8.2.4", "multer": "^1.4.2", "openapi-to-graphql": "^2.6.3", "oracledb": "^5.2.0", "passport": "^0.4.0", "passport-jwt": "^4.0.0", "prom-client": "^15.1.3", "prompts": "^2.1.0", "request": "^2.88.0", "scan-fs": "0.0.5", "socket.io": "^2.3.0", "through2": "^3.0.0", "typescript": "^4.8.3", "ws": "latest"}, "devDependencies": {"@loopback/build": "^1.0.1", "@loopback/testlab": "^1.0.1", "@types/node": "^10.11.2", "@types/node-fetch": "^2.6.3", "@types/passport": "^0.4.7", "@types/passport-jwt": "^3.0.1", "query-string": "^6.2.0", "request-promise": "^4.2.5"}}