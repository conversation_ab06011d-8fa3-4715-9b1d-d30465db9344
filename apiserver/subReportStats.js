const reportApiCallStats = require("./reportApiCallStats");
const log = require('./dist').log.audit;
let timer;
process.on('message', function(msg){
  if(msg === 'ping'){
    if(timer){
      try {
        clearTimeout(timer);
        timer = null;
      }catch (e) {}
    }
  }else if(msg.type === 'start'){
    reportApiCallStats.start();
  }else if(msg.type === 'stop'){
    reportApiCallStats.stop();
  }else if(msg.type === 'apiCell'){
    log.info(JSON.stringify(msg.data));
    reportApiCallStats.put(msg.data);
  }
});

setInterval(()=>{
  try {
    process.send('ping');
  }catch (e) {
    reportApiCallStats.stop(true);
  }
  timer = setTimeout(()=>{
    reportApiCallStats.stop(true);
  },5000);
},10000)
