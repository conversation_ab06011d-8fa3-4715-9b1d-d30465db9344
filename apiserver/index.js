// const fork = require('child_process').fork;
const appConfig = require('./config');
const cp=require('child_process');
// const getToken = require('./tapdata').getToken;
// const request = require('request');
const Conf = require('conf');
const config = new Conf();

config.clear();
config.set(appConfig);

const log = require('./dist').log.app;
const TapDataCodeGenerator = require('./generators/tapDataCodeGenerator');
const datasource = require('./datasource');
const fs = require('fs');
const cpus = require('os').cpus().length;
const cluster = require('cluster');
const {sign} = require('jsonwebtoken');

log.info('Config file at: ', `${__dirname}/config.js`);
log.info('Current active config is: \n', appConfig);

//const reportApiCallStats = require("./reportApiCallStats");
//require("./reportApiCallStatsBatchLogic");

const name = 'api-server-' + (config.get('port') || +process.env.PORT || 3080);

const maxRestartCount = 30;
let report
function forkSubReport(){
	report=cp.fork('./subReport');
	report.on('message',function(msg) {
		if (msg === 'ping') {
			report.send('ping');
			return;
		}
	});
	let Interval = setInterval(()=>{
		try{
			if(!report.connected){
				clearInterval(Interval);
				try {
					report.kill();
				} catch (e) {
					log.warn('report process disconnect:', e);
				}
				setTimeout(function() {
					forkSubReport();
				}, 2000);
			}
		}catch (e) {
			log.error(e)
		}
	},5000);
	report.on('disconnect', (err) => {
		log.warn('report process disconnect:', err);
		try {
			report.kill();
		} catch (e) {
			log.warn('report process disconnect:', e);
		}
		setTimeout(function() {
			forkSubReport();
		}, 2000);
	});
}
forkSubReport()

let reportStats;
function forkSubReportStats() {
	reportStats = cp.fork('./subReportStats');
	reportStats.on('message',function(msg) {
		if (msg === 'ping') {
			reportStats.send('ping');
			return;
		}
	});
	let Interval = setInterval(()=>{
		try{
			if(!reportStats.connected){
				clearInterval(Interval);
				try {
					reportStats.kill();
				} catch (e) {
					log.warn('report process disconnect:', e);
				}
				setTimeout(function() {
					forkSubReportStats();
				}, 2000);
			}
		}catch (e) {
			log.error(e)
		}
	},5000);
	reportStats.on('disconnect', (err) => {
		log.warn('reportStats process disconnect:', err);
		try {
			reportStats.kill();
		} catch (e) {
			log.warn('reportStats process disconnect:', e);
		}
		setTimeout(function() {
			forkSubReportStats();
		}, 2000);
	});
}
forkSubReportStats();

class Main {
	constructor(props) {

		/**
		 * 应用工作进程
		 * @type {ChildProcess}
		 */
		this.appWorkers = {};

		/**
		 * 配置文件变化监听进程
		 * @type {ChildProcess}
		 */
		this.configMonitor = null;

		this.workerStatus = {
			workers: null,//workers info array
			worker_process_id: null,
			worker_process_start_time: null,
			worker_process_end_time: null,
			status: 'stop',
			exit_code: null
		}
	}

	/**
	 * 启动
	 */
	start() {

		Object.assign(this.workerStatus, {
			workers: [],
			worker_process_id: '',
			worker_process_start_time: new Date().getTime(),
			worker_process_end_time: null,
			status: 'starting'
		});
		if(report && report.connected) {
			report.send(this.workerStatus)
		}
		if (appConfig.metrics && appConfig.metrics.enable)
			require('./monitor/registry').initRegistry();
		// 启动 app 工作进程
		this.startApp();

		// 监听配置文件变化
		let that = this;
		if (config.get('model') === 'cloud') {
			function forkSubProcess() {
				let child = cp.fork('./subProcess');
				let Interval = setInterval(() => {
					try {
						if(!child.connected){
							clearInterval(Interval);
							try {
								child.kill();
							} catch (e) {
								log.warn('report process disconnect:', e);
							}
							setTimeout(function() {
								forkSubProcess();
							}, 2000);
						}
					} catch (e) {
						log.error(e)
					}
				}, 5000);
				child.on('message', function(msg) {
					if (msg === 'ping') {
						child.send('ping');
						return;
					}
					const statusList = ['deploy_fail', 'deploying'];
					if (msg === 'restart') {
						that.restartApp();
					} else if (statusList.includes(msg)) {
						that.workerStatus.status = msg;
						if (report && report.connected) {
							report.send(that.workerStatus);
						}
					}
				});
			}
			forkSubProcess()
			//this.startConfigChangeMonitor();
			//datasource.start();
		}

		// report api call record
		if(reportStats && reportStats.connected) {
			reportStats.send({type: 'start'});
		}
		//reportApiCallStats.start();

		setInterval(() => {
			Object.keys(cluster.workers).forEach(id => {
				this.appWorkers[id].worker_status = cluster.workers[id].state;
				this.appWorkers[id].pid = cluster.workers[id].process.pid;
			});
			this.workerStatus.workers = this.appWorkers;
			if(report && report.connected) {
				report.send(this.workerStatus);
			}
		},10000);

		if(config.get('springCloudEnable')) {
			log.info('enable spring cloud')
			require('./spring_cloud/eureka_client').start();
		}
	}

	/**
	 * 停止
	 */
	stop() {

		let workerIds = Object.keys(cluster.workers || this.appWorkers || {});
		if (workerIds.length > 0) {
			workerIds.forEach(id => {
				let worker = cluster.workers[id];
				worker.destroy();
				log.info(`${worker.id} worker process exited.`);
			});
			log.info(`Processes of app workers have stopped.`);
		}
		Object.assign(this.workerStatus, {
			workers: null,
			running_thread: 1,
			total_thread: 1,
			status: 'stop',
		});
		if(report && report.connected) {
			report.send(this.workerStatus);
		}

		if (this.configMonitor) {
			this.configMonitor.stop();
			log.info('configMonitor process exited.');
		}
		if(reportStats && reportStats.connected) {
			reportStats.send({type: 'stop'});
		}
		if(report && report.connected) {
			report.kill();
		}
		//reportApiCallStats.stop();
		datasource.stop();
	}

	forkWorker() {
		let me = this;
		let stdio = [process.stdin,process.stdout,process.stderr,'ipc'];
		cluster.setupMaster({
			exec: `${__dirname}/app.js`,
			args: process.argv.slice(2),
			silent: true,
			stdio:stdio
		});
		let worker = cluster.fork();
		worker.on('exit', (code, signal) => {
			log.warn('process ' + worker.id + ' exit, code is ' + code + ', signal is ' + signal)
			if( code !== 0 ){

				log.warn('process ' + worker.id + ' exit, code is ' + code + ', signal is ' + signal + ', restart it.')

				setTimeout( function() { me.restartWorkerById(worker.id); }, 2000);

			}
		});
		worker.on('disconnected', (code) => {

			log.warn('process ' + worker.id + ' disconnected, code is ' + code + ', restart it.')

			// setTimeout( () => { me.restartWorkerById(worker.id); }, 2000);
			worker.destroy();
		});

		worker.on('message', (msg) => {
			if (msg.type === 'status') {

				Object.keys(cluster.workers).forEach(id => {
					if( me.appWorkers[id] === worker) {
						me.appWorkers[id].worker_status = msg.data;
						me.appWorkers[id].worker_msg = msg.msg;
					}
				});
				Object.assign(this.workerStatus, {
					workers: me.appWorkers,
					status: 'running',
					exit_code: null
				});
				if(report && report.connected) {
					report.send(this.workerStatus);
				}
			} else if( msg.type === 'apiCell') {
				if(reportStats && reportStats.connected) {
					reportStats.send(msg)
				}
				//reportApiCallStats.put(msg.data);
			}
		});
		return worker;
	}

	/**
	 * 启动 app 进程
	 */
	startApp() {

		const workerCount = appConfig.api_worker_count === 0 ? cpus : appConfig.api_worker_count;
		let me = this;

		for( let i = 0; i < workerCount; i++){

			let worker = this.forkWorker();

			this.appWorkers[worker.id] = {
				id: worker.id,
				worker_status: worker.state,
				worker_start_time: new Date().getTime()
			};
		}
	}

	/**
	 * 重启 app 进程
	 */
	restartApp() {

		let workerIds = Object.keys(cluster.workers || this.appWorkers || {});
		if (workerIds.length > 0) {

			let me = this;
			/*workerIds.forEach(id => {
				this.restartWorkerById(id)
			});*/
			let start = function(idx){
				if( workerIds.length > idx && workerIds[idx]) {
					me.restartWorkerById(workerIds[idx], function(){
						idx++;
						start(idx);
					});
				}
			};
			start(0);
		} else {
			this.startApp();
		}
	}

	restartWorkerById(id, cb) {
		log.info('restart worker ' + id + ', process id is ' + (cluster.workers[id] ? cluster.workers[id].process.pid : '-1'));

		let oldWorker =  this.appWorkers[id]
		if( oldWorker ){
			let workerProcess = cluster.workers[oldWorker.id]
			if( workerProcess ){
				workerProcess.destroy();
			}

			let newWorker = this.forkWorker();
			this.appWorkers[newWorker.id] = Object.assign(oldWorker, {
				id: newWorker.id,
				worker_status: newWorker.state,
				worker_start_time: new Date().getTime()
			});

			if( cb ){
				newWorker.on('message', (msg) => {
					if( msg && msg.type === 'status'){
						cb(newWorker);
					}
				})
			}

		}

		delete this.appWorkers[id];
	}
}

const main = new Main();
main.start();

if (config.get('model') === 'local') {
	const localConfigFilePath = config.get('apiFile');
	if (fs.existsSync(localConfigFilePath)) {
		let config = fs.readFileSync(localConfigFilePath).toString();
		config = JSON.parse(config || '{}');
		main.generate(config);
	}
}


let exitHdl = function (signal) {
	log.info("Stopping api server...");
	main.stop();
};
process.on('beforeExit', exitHdl);
process.on('multipleResolves', (type, promise, reason) => {
	log.error(type, promise, reason);
});
process.on('uncaughtException', (err, origin) => {
	fs.writeSync(
		process.stderr.fd,
		`捕获的异常: ${err}\n` +
		`异常的来源: ${origin}`
	);
	console.error(err,origin)
});
process.on('unhandledRejection', (reason, promise) => {
	log.error('未处理的拒绝：', promise, '原因：', reason);
});
function handle(signal) {
	log.error(`接收到 ${signal}`);
	if (signal === 'SIGINT')
		process.exit(1);
}
process.on('SIGINT', handle);
process.on('SIGTERM', handle);
