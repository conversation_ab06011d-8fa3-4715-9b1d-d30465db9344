import { BootMixin } from '@loopback/boot';
import { ApplicationConfig } from '@loopback/core';
import { RepositoryMixin } from '@loopback/repository';
import { RestApplication, SequenceActions, RestBindings, RequestBodyParserOptions, DefaultSequence } from '@loopback/rest';
import { ServiceMixin } from '@loopback/service-proxy';
import { MySequence } from './sequence';
import * as path from 'path';
// const appConfig = require('../../config');
const Conf = require('conf');
const config = new Conf();

import {
  AuthenticationComponent,
  AuthenticationBindings,
} from '@loopback/authentication';
import { AuthStrategyProvider, SendProvider, RejectProvider } from './providers';

export class ApiGatewayApplication extends BootMixin(
  ServiceMixin(RepositoryMixin(RestApplication)),
) {
  constructor(options: ApplicationConfig = {}) {
    super(options);

    this.sequence(DefaultSequence);
    this.component(AuthenticationComponent);
    this.bind(AuthenticationBindings.STRATEGY).toProvider(
      AuthStrategyProvider,
    );
    this.bind(SequenceActions.SEND).toProvider( SendProvider );
    this.bind(SequenceActions.REJECT).toProvider( RejectProvider );

    let requestBodyParseOptions = {
		validation: {
			formats: {
				int32: function(str: any) {
					if( typeof str === 'string' ) {
						if( /^-?\d{1,10}$/.test(str) ){
						  str = Number(str);
                        } else {
						  return false;
                        }
					}
					if ( typeof str === 'number') {
                      return str >= -********** && str <= ********** && Math.round(str) === str
					} else {
                      return false;
                    }
                },
				int64: function(str: any){
					if( typeof str === 'string' ) {
						if( /^-?\d{1,19}$/.test(str) ){
							str = Number(str);
						} else {
							return false;
						}
					}
					if ( typeof str === 'number') {
						return str >= -9223372036854775808 && str <= 9223372036854775807 && Math.round(str) === str
					} else {
						return false;
					}
                },
                double: /^-?\d+(\.\d+)?$/,
				float: /^-?\d+(\.\d+)?$/,
				byte: /^\d+$/,
            }
        }
    };
    // @ts-ignore
	  this.bind(RestBindings.REQUEST_BODY_PARSER_OPTIONS).to(requestBodyParseOptions);

    // Set up the custom sequence
    this.sequence(MySequence);

    const startTime = new Date();

    // Set OpenAPI specification
    this.api({
      openapi: '3.0.0',
      info: {
        title: 'Tapdata OpenAPI',
        version: config.get('version'),
      },
      paths: {},
      servers: [{ url: '/' }],
      externalDocs: {
        description: 'Find out more about Tapdata.',
        url: 'https://tapdata.io',
      },
      components: {
        'securitySchemes': {
          /*"ApiKeyAuth": {
            "type": "apiKey",
            "in": "header",
            "name": "access_token"
          },*/
          'OAuth2': {  //arbitrary name for the security scheme
            'type': 'oauth2',
            'flows': {
              'clientCredentials': {
                'tokenUrl': (config.get('oAuthBaseUrl') || '') + '/oauth/token',
                'scopes': {},
              },
              'implicit': {
                'authorizationUrl': (config.get('oAuthBaseUrl') || '') + '/oauth/authorize',
                'scopes': {},
              },
            },
          },
        },
      },
      'security': [
        {
          'OAuth2': [],
          //"ApiKeyAuth": []
        },
      ],
    });

    // Set up default home page
    this.static('/', path.join(__dirname, '../../public'));

    this.route('get', '/status', {
		summary: "health check",
		responses: {
			'200': {
				description: 'health check',
				content: 'application/json',
			}
        }
    }, function(){
      return `{"status": "UP"}`;
    });

      this.route('get', '/openapi-generator', {
          parameters: [{name: 'path', in: 'query', schema: {type: 'string'}}],
          responses: {
              '200': {
                  description: 'health check',
                  content: 'application/json',
              }
          }
      }, async function (path:string) {
          const request = require("request-promise");
          let port = config.get('port') || process.env.PORT || 3030;
          console.info('apiserver port:',port);
          let a = await request({
              'url':'http://127.0.0.1:'+port+'/openapi.json',
              'method':'GET'
          });
          let openapi:any;
          try{
              if(!path)path = '/api/v1/Settings/cust/aaa';
              openapi = JSON.parse(a);
              let thisPath = openapi['paths'][path];
              openapi['paths'] = {};
              openapi['paths'][path] = thisPath;
          }catch(e){
              console.error(e);
          }
          return openapi;
      });
    this.route('get', '/openapi-readOnly.json', {
      parameters: [{name: 'path', in: 'query', schema: {type: 'string'}}],
      responses: {
        '200': {
          description: 'onlyRead config',
          content: 'application/json',
          // schema:{"type": "object"}
        }
      }
    }, async function (path:string) {
      const request = require("request-promise");
      let port = config.get('port') || process.env.PORT || 3030;
      console.info('apiserver port:',port);
      let a = await request({
        'url':'http://127.0.0.1:'+port+'/openapi.json',
        'method':'GET'
      });
      let openapi:any;
      try{
        const readOperations = ['count','findPage_post','findById','findPage','customerQuery_post','customerQuery']
        openapi = JSON.parse(a);
        for(let x in openapi.paths){
          let havePath = false;
          for(let y in openapi.paths[x]){
            if(!readOperations.includes(openapi.paths[x][y]['x-operation-name'])){
              delete openapi.paths[x][y]
            }else{
              havePath = true;
            }
          }
          if(!havePath){
            delete openapi.paths[x];
          }
        }
      }catch(e){
        console.error(e);
      }
      return openapi;
    });
    this.projectRoot = __dirname;
    // Customize @loopback/boot Booter Conventions here
    if ((config.get('metrics') || {}).enable) {
      function sendText(res: any, data: any, contentType = 'text/plain', statusCode = 200) {
        res.writeHead(statusCode, {
          'Content-Type': contentType,
          'Access-Control-Allow-Origin': '*'
        });
        res.end(data);
      }

      const { initRegistry, getAggregatedMetrics, getContentType, getMetrics } = require('../../monitor/registry')
      console.log(`Initializing metrics registry in worker process ${process.pid}`);
      initRegistry();

      this.route('get', '/metrics', {
        summary: "prometheus metrics",
        responses: {
          '200': {
            description: 'prometheus metrics',
            content: getContentType(),
          }
        }
      }, async () => {
        console.log(`/metrics endpoint called in process ${process.pid}`);
        try {
          const metrics = await getAggregatedMetrics();
          console.log(`Successfully got aggregated metrics, length: ${metrics.length}`);
          return metrics;
        } catch (error: any) {
          console.warn(`Worker ${process.pid} failed to get aggregated metrics:`, error.message);
          const workerMetrics = await getMetrics();
          console.log(`Fallback to worker metrics, length: ${workerMetrics.length}`);
          return `# Aggregated metrics unavailable (${error.message}), showing worker ${process.pid} metrics only\n${workerMetrics}`;
        }
      })
    }
    this.bootOptions = {
      controllers: {
        // Customize ControllerBooter Conventions here
        dirs: ['controllers'],
        extensions: ['.controller.js'],
        nested: true,
      },
    };
  }
}
