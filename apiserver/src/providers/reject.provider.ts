import {LogError, Reject, HandlerContext, RestBindings} from '@loopback/rest';
import {inject, Provider} from '@loopback/context';
import {HttpError} from 'http-errors';
import {writeErrorToResponse, ErrorWriterOptions} from 'strong-error-handler';
import {ResponseAdapter} from './adapter';

// TODO(bajtos) Make this mapping configurable at RestServer level,
// allow apps and extensions to contribute additional mappings.
const codeToStatusCodeMap: {[key: string]: number} = {
	ENTITY_NOT_FOUND: 404,
};

export class RejectProvider implements Provider<Reject> {
	constructor(
		@inject(RestBindings.SequenceActions.LOG_ERROR)
		protected logError: LogError,
		@inject(RestBindings.ERROR_WRITER_OPTIONS, {optional: true})
		protected errorWriterOptions?: ErrorWriterOptions,
	) {}

	value(): Reject {
		return (context, error) => this.action(context, error);
	}

	action({request, response}: HandlerContext, error: Error) {
		const err = <HttpError>error;

		if (!err.status && !err.statusCode && err.code) {
			const customStatus = codeToStatusCodeMap[err.code];
			if (customStatus) {
				err.statusCode = customStatus;
			}
		}

		const statusCode = err.statusCode || err.status || 500;
		let errorCode = "100500";
		// response adapter
		if(request.headers['client_name']){
			let result = ResponseAdapter.wrap_response_result(request, response, err);
			response.statusCode = 200;
			if(process.env['ERROR_CODE'] === "true"){
				result = result ? JSON.stringify({status:"ok",data:result}) : undefined;
			}else{
				result = result ? JSON.stringify(result) : undefined;
			}
			response.end(result);
		} else {
			if(process.env['ERROR_CODE'] === "true") {
				if (error.name === 'UnauthorizedError') {
					errorCode = "100401";
				} else if (error.name === 'NotFoundError') {
					errorCode = '100410';
				} else if (error.name === 'BadRequestError') {
					// @ts-ignore
					if ( ['INVALID_PARAMETER_VALUE','ER_BAD_FIELD_ERROR'].includes(error['code'])) {
						errorCode = '100411';
						// @ts-ignore
					} else if (error['code'] === 'ER_BAD_FIELD_ERROR') {
						errorCode = '100412';
					}
				}
				// @ts-ignore
				if (error && error['code'] === 'ER_BAD_FIELD_ERROR') {
					errorCode = '100412';
				}
				// @ts-ignore
				response['tapErrorCode'] = errorCode;
				// @ts-ignore
				response['tapErrorMsg'] = error.toString();
				response.end(JSON.stringify({status: "error", errorCode: errorCode, errorMsg: error.toString()}));
			}else{
				writeErrorToResponse(err, request, response, this.errorWriterOptions);
			}
		}
		this.logError(error, statusCode, request);
	}
}
