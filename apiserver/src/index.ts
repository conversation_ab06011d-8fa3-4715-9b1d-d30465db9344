import {ApiGatewayApplication} from './application';
import {ApplicationConfig} from '@loopback/core';
import {log} from './log';
import { createGraphQLSchema } from 'openapi-to-graphql';
import { graphqlHTTP } from 'express-graphql';
import fetch from 'node-fetch';
const Conf = require('conf');
const config = new Conf();
export {ApiGatewayApplication};

export async function main(options: ApplicationConfig = {}, cb ?: (result : boolean) => {}) {
  const app = new ApiGatewayApplication(options);
  await app.boot();
  await app.start();
  const SocketServer = require('ws').Server;
  const wss = new SocketServer(app.restServer['_httpServer']);
  wss.on('connection', function connection(ws:any, re:any) {
      let client:any;
      let changeStream:any;
      function timeOut(){
          return setTimeout(function(){
              console.info('time out!');
              try {
                  ws.close();
              }catch (e) {
                  log.app.error(e);
              }
              try {
                  changeStream.close();
                  client.close();
              }catch (e) {
                  log.app.error(e);
              }
          }, 10000);
      }
      let timeOutObj = timeOut();
      ws.on('message', function incoming(message:string) {
          console.log('received: %s', message);
          clearTimeout(timeOutObj);
          timeOutObj = timeOut();
          if('ping' === message){
              ws.send('pong');
              return;
          }
          try{
              message = JSON.parse(message);
          }catch(e){
              log.app.error(e);
          }
          const request = require("request");
          (async () => {
              let accessToken = re.url.split('access_token=');
              if(accessToken.length>1){
                  accessToken = accessToken[1];
                  accessToken = accessToken.split("&")[0];
              }else{
                  accessToken = '';
              }
              request({
                  'url':'http://' + re.headers.host + re.url,
                  'method':'GET',
                  headers: {
                      "access_token":accessToken
                  }
              }, async function (error:any, response:any, body:any) { //127.0.0.1:3080/api/v1/user/
                  if (error || response.statusCode !== 200) {
                      ws.send('Unauthorized');
                      ws.close();
                      return;
                  }
                  if (!error && response.statusCode === 200) {
                      try {
                          console.log(body); // Show the HTML for the baidu homepage.
                          body = JSON.parse(body);
                          let options: any = {};
                          //console.log(message.indexOf("next-message-of-"));
                          // @ts-ignore
                          //if(message['fullDocument']){
                              options['fullDocument'] = 'updateLookup';
                          //}
                          // @ts-ignore
                          if (message['resumeAfter'] && message['resumeAfter'] !== '') {
                              // @ts-ignore
                              options['resumeAfter'] = {"_data": message['resumeAfter']}
                          }
                          const MongoClient = require('mongodb').MongoClient;
                          let mongoUri = (body.url).replace(/&amp;/g,'&');//"mongodb://192.168.1.99:27017/tapdata";
                          let mongoOpts: any = {
                              //'allowExtendedOperators':body.allowExtendedOperators,
                              'useNewUrlParser': body.useNewUrlParser
                              //'useUnifiedTopology': true
                          };
                          if (body.ssl === true || body.ssl === 'true') {
                              mongoOpts.ssl = true;
                              mongoOpts.sslCA = body.sslCA;
                          }
                          client = new MongoClient(mongoUri, mongoOpts);
                          await client.connect();
                          const db = client.db();
                          if (body.filter) {

                          }

                          function formatWhere(where: any) {
                              if (Array.isArray(where)) {
                                  where.forEach(x => {
                                      formatWhere(x);
                                  });
                              } else {
                                  for (let x in where) {
                                      if (typeof where[x] === 'object') {
                                          formatWhere(where[x]);
                                      }
                                      if (x.substring(0, 1) !== '$') {
                                          let newKey = 'fullDocument.' + x;
                                          where[newKey] = where[x];
                                          delete where[x];
                                      }
                                  }
                              }
                          }

                          let pipeline: any = [];
                          if (body['filter']) {
                              formatWhere(body['filter']);
                              pipeline.push({'$match': {"$or":[body['filter'],{'operationType':'delete'}]}});
                          }
                          if (body['fields']) {
                              formatWhere(body['fields']);
                              body['fields'].operationType = 1;
                              body['fields'].ns = 1;
                              body['fields'].documentKey = 1;
                              body['fields'].updateDescription = 1;
                              pipeline.push({'$project': body['fields']});
                          }
                          changeStream = await db.collection(body['collection']).watch(pipeline, options);
                          changeStream.on('change', (next: any) => {
                              try {
                                  ws.send(JSON.stringify(next));
                                  //console.info(next);
                              } catch (e) {
                                  console.info(e);
                                  log.app.error(e);
                                  client.close();
                                  ws.close();
                              }
                          });
                      }catch(e){
                          log.app.error(e);
                      }
                  }
              });
          })();
      })
  });

  const url = app.restServer.url;
	log.app.info(`Server is running at ${url}`);
	console.log(`Server is running at ${url}`);

	if( cb )
	  cb(true);
  
  const graphqlPath = '/graphql';
  let port = config.get('port') || process.env.PORT || 3080;
  const openapiReadOnlyUrl = `http://127.0.0.1:${port}/openapi-readOnly.json`
  const openApiSchema = openapiReadOnlyUrl;

  const oas = await fetch(openApiSchema)
    .then((res: any) => {
      console.log(`JSON schema loaded successfully from ${openApiSchema}`);
      return res.json();
    })
    .catch((err: any) => {
      console.error('ERROR: ', err);
      throw err;
    });
    
  const {schema} = await createGraphQLSchema(oas, {
    strict: false,
    viewer: false,
    baseUrl: url,
    headers: {
      'X-Origin': 'GraphQL',
    },
    tokenJSONpath: '$.jwt',
  });
  const handler = graphqlHTTP(
    (request: any, response: any, graphQLParams: any) => ({
      schema,
      pretty: true,
      graphiql: true,
      context: {jwt: getJwt(request)},
    }),
  );

  // Get the jwt from the Authorization header and place in context.jwt, which is then referenced in tokenJSONpath
  function getJwt(req: any) {
    if (req.headers && req.headers.authorization) {
      return req.headers.authorization.replace(/^Bearer /, '');
    }
  }

  app.mountExpressRouter(graphqlPath, handler);

  console.log(`Graphql API: ${url}${graphqlPath}`);
  return app;
}

export * from './log';

