/**
 * <AUTHOR>
 * @date 1/8/20
 * @description
 */
import * as Excel from 'exceljs';
import {Cell, Row, ValueType, Workbook} from 'exceljs';

class ExcelUtil {
	constructor(){}

	generatorTemplate(data: any[][], sheetName?: string){

		let wb = new Excel.Workbook();

		let ws = wb.addWorksheet(`${sheetName || 'Sheet 1'}`);
		ws.views = [
			{state: 'frozen', xSplit: 0, ySplit: 1}
		];

		// header
		data.forEach(rowData => {
			ws.addRow(rowData);
		});

		return wb.xlsx.writeBuffer();

	}

	readFromFiles(filePaths: string[]): Promise<object> {
		return new Promise(((resolve, reject) => {

			let promises:Promise<Workbook>[] = [];
			filePaths.forEach( filePath => {
				let workbook = new Excel.Workbook();
				// @ts-ignore
				workbook.filePath = filePath;
				promises.push(workbook.xlsx.readFile(filePath));
			});

			Promise.all(promises).then((workbooks) => {

				let result = {};

				workbooks.forEach(workbook => {

					let workbookData = {};
					workbook.eachSheet( worksheet => {

						let workbookSheetData:any[] = [];
						let sheetName = worksheet.name;
						// @ts-ignore
						workbookData[sheetName] = workbookSheetData;

						let fields: string[];
						worksheet.eachRow( row => {

							if( !fields ){
								let rowData: any [] = [];
								row.eachCell(cell => {
									rowData.push(this.getCellValue(cell) || '');
								});
								fields = this.getFieldsFromHeader(rowData);
							} else {

								let record = {};
								for( let col = 0; col < fields.length; col++){

									let value = this.getCellValue(row.getCell(col + 1));
									if( value ){
										// @ts-ignore
										record[fields[col]] = value;
									}
								}

								// @ts-ignore
								workbookSheetData.push(record);
							}
						});
					});

					// @ts-ignore
					result[workbook.filePath] = workbookData
				});

				resolve(result);
			}).catch((errors) => {

				// TODO: warp error

				reject(errors);
			});

		}));
	}

	private getCellValue(cell: Cell): any{

		let text: any = null;

		if( cell.type === ValueType.Merge
		  || cell.type === ValueType.Number
		  || cell.type === ValueType.String
		  || cell.type === ValueType.Date
		  || cell.type === ValueType.Boolean
		){
			text = cell.value;
		} else if( cell.type === ValueType.Null ){
			text = "";
		} else if( cell.type === ValueType.Formula){
			// @ts-ignore
			text = cell.value.result;
		} else if( cell.type === ValueType.Hyperlink){
			// @ts-ignore
			text = `${cell.value.text}(${cell.value.hyperlink})`;
		} else if( cell.type === ValueType.SharedString){
			// @ts-ignore
			text = `${cell.value.result}`
		} else if( cell.type === ValueType.RichText){
			// @ts-ignore
			text = cell.value.richText.reduce( (s, k) => s.text+k.text);
		} else if( cell.type === ValueType.Error){
			// @ts-ignore
			text = cell.error;
		}

		return text;
	}

	private getFieldsFromHeader(colHeader: string[]) {
		return colHeader.map( field => {
			if( !field)
				return field;
			field = field.replace(/（/g, '(').replace(/）/g, ')');
			if( field.indexOf('(') >= 0 ){
				let matchRes = field.match(/\((.*)\)/);
				if( matchRes && matchRes.length >= 2)
					return matchRes[1];
				else
					return field;
			} else {
				return field;
			}
		});
	}
}

export const importExcel = new ExcelUtil();
