const tapdata = require('./tapdata');
const requestOfcalls = require('request');
const Conf = require('conf');
const config = new Conf();

let apiCellCache = [];
let enableReportApiCall = config.get("apiStatsBatchReport.enableApiStatsBatchReport") === 'true' ||
	config.get("apiStatsBatchReport.enableApiStatsBatchReport") === true;
let sizeOfTriggerApiStatsBatchReport =
	Number(config.get("apiStatsBatchReport.sizeOfTriggerApiStatsBatchReport")) || 1000;
let timeSpanOfTriggerApiStatsBatchReport =
	Number(config.get('apiStatsBatchReport.timeSpanOfTriggerApiStatsBatchReport')) || 5000;
let intervalId;

const start = exports.start = function(){
	if( intervalId )
		clearInterval(intervalId);

	intervalId = setInterval(() => {
		reportApiCallStats();
	}, timeSpanOfTriggerApiStatsBatchReport);
};
const stop = exports.stop = function(){
	clearInterval(intervalId);
	reportApiCallStats(true);
};

exports.put = function(apiCell){
	if( enableReportApiCall ){
		apiCellCache.push(apiCell);
		if( apiCellCache.length >= sizeOfTriggerApiStatsBatchReport )
			reportApiCallStats();
	}

};

tapdata.on('apiStatsBatchReport.sizeOfTriggerApiStatsBatchReport:changed', (newVal, oldValue) => {
	sizeOfTriggerApiStatsBatchReport = Number(newVal) || 1000;
});
tapdata.on('apiStatsBatchReport.timeSpanOfTriggerApiStatsBatchReport:changed', (newVal, oldValue) => {
	timeSpanOfTriggerApiStatsBatchReport = Number(newVal) || 5000;
	start();
});
tapdata.on('apiStatsBatchReport.enableApiStatsBatchReport:changed', (newVal, oldValue) => {
	if( newVal === 'true' || newVal === true) {
		enableReportApiCall = true;
		start();
	} else {
		enableReportApiCall = false;
		stop();
	}
});



function reportApiCallStats(isKill) {

	let apiAuditLogs = apiCellCache;//apiCellCache.splice(0, apiCellCache.length);
	apiCellCache = [];
	if (apiAuditLogs.length > 0) {
		apiAuditLogs.forEach((apicall) => {
			apicall.report_time = new Date().getTime();
		});
		tapdata.getToken(function (token) {
			let url = config.get("tapDataServer.url") + '/api/ApiCalls?access_token=' + token;
			requestOfcalls.post({
				url: url,
				json: true,
				body: apiAuditLogs,
				gzip: true,
			}, (err, resp, body) => {
				if (err || body.error) {
					console.error('<EMAIL>:47:', err, body);
				} else if (resp.statusCode === 401 || resp.statusCode === 403) {
					apiCellCache.push(...apiAuditLogs);
					console.error('Access token Expired');
					tapdata.removeToken();
				} else {
					//let reported = Array.isArray(body) ? body : [body];
				}
				if(isKill){
					process.exit(0)
				}
			});
		});

	}
}
