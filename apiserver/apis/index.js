'use strict';

const MongoAPI = require('./mongoAPI');
const CRUDAPI = require('./crudAPI');

class APIFactory {

  createAPI (dataSource, apiConfig) {
    switch (dataSource.getType()) {
      case 'mongodb':
        return new MongoAPI(dataSource, apiConfig);
      case 'db2':
      case 'mssql':
      case 'mysql':
      case 'oracle':
      case 'postgresql':
        return new CRUDAPI(dataSource, apiConfig);
      default:
        throw new Error(`unsupported data source type ${dataSource.getType()}`);
    }
  }
}

module.exports = new APIFactory();
