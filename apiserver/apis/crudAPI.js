'use strict';

const _ = require('lodash');

const Conf = require('conf');
const conf = new Conf();

const log = require('../dist').log.app;

const TsType2APITypes = {
  string: ['objectid', 'string', 'null', 'uuid'],
  boolean: ['boolean'],
  number: ['integer', 'int', 'short', 'long', 'double', 'number', 'float', 'decimal128', 'bigdecimal', 'byte', 'bigint','int32','int64'],
  date: ['date', 'datetime', 'timestamp', 'timestamptz'],
  object: ['document', 'object', 'map', 'jsonb', 'json', 'blob'],
  array: ['array', 'arraylist'],
  buffer: ['bytes']
};

const OpenAPIType2APITypes = {
  string: ['objectid', 'string', 'text', 'char', 'varchar', 'uuid'],
  boolean: ['boolean'],
  integer: ['int', 'integer', 'short', 'int32', 'int64'],
  long: ['long', 'bigint'],
  float: ['float'],
  double: ['double', 'decimal128', 'bigdecimal'],
  dateTime: ['date', 'datetime', 'timestamp', 'timestamptz'],
  byte: ['byte'],
  binary: ['bytes'],
  password: ['password']
};

const OpenAPIDataTypes = {
  integer: { type: 'integer', format: 'int32', description: '' },
  long: { type: 'integer', format: 'int64', description: '' },
  float: { type: 'number', format: 'float', precision: 0 },
  double: { type: 'number', format: 'double', precision: 0 },
  string: { type: 'string', length: 0 },
  byte: { type: 'string', format: 'byte', description: '', length: 0 },
  binary: { type: 'string', format: 'binary', description: '', length: 0 },
  boolean: { type: 'boolean' },
  date: { type: 'string', format: 'date', description: '' },
  dateTime: { type: 'string', format: 'date-time', description: '' },
  password: { type: 'string', format: 'password', description: '' }
};

const TapTypeToDataType = {
  "1":"dateTime",
  "2":"array",
  "3":"boolean",
  "4":"object",
  "5":"string",
  "6":"dateTime",
  "7":"binary",
  "8":"number",
  "9":"binary",
  "10":"string",
  "11":"dateTime"
}

class CRUDAPI {

  constructor (dataSource, apiConfig) {
    this._validate(dataSource, apiConfig);
    this._ds = dataSource;
    this._api = apiConfig;
    this._valid = true;
    this._model = this._generateModel();
    this._controller = this._generateController();
    this._repository = this._generateRepository();
  }

  _validate (dataSource, apiConfig) {
    if (dataSource.getId() !== apiConfig.datasource) {
      throw new Error(`api ${apiConfig.id} expects data source ${apiConfig.datasource} but got ${dataSource.getId()}`);
    }
    if (!apiConfig.tablename && apiConfig.tableName){
      apiConfig.tablename = apiConfig.tableName
    }
    if (!apiConfig.tablename) throw new Error(`missing table name for api ${apiConfig.id}`);
    if (!apiConfig.fields) throw new Error(`missing fields for api ${apiConfig.id}`);
    if (!Array.isArray(apiConfig.fields)) throw new Error(`invalid fields for api ${apiConfig.id}`);
  }

  isValid () {
    return this._valid;
  }

  getDataSource () {
    return this._ds;
  }

  getModel () {
    return this._model;
  }

  getController () {
    return this._controller;
  }

  getRepository () {
    return this._repository;
  }

  _generateModel () {
    const adapted = {
      apiVersion: this._api.apiVersion,// || 'v1',
      tablename: this._api.tablename,
      dataSourceName: this._ds.getName(),
      dataSource: this._api.datasource,
      apiId: this._api.id,
      apiName: this._api.description,
      basePath: this._api.basePath,
      description: this._api.description,
      fields: this._convertFields(),
      prefix: this._api.prefix || '',
      schema: this._ds.config.settings.schema,
      limit: this._api.limit, // 添加 limit 字段支持
      nameAliasMapping: this._genericNameAliasMapping()
    };

    adapted.paths = this._api.paths.map(api => {
      const convertedAPI = {
        allPathId: this._api.id,
        requiredQueryField: api.requiredQueryField || [],
        availableQueryField: api.availableQueryField || [],
        pathTpl: api.path,
        method: api.method,
        rawName: api.name,
        result: api.result,
        type: api.type,
        description: api.description || '',
        roles: api.acl || [],
        parameters: api.params,
        where: api.where,
        sort: api.sort,
        select: api.fields,
        fields: api.fields,
        fullCustomQuery: api.fullCustomQuery || false,
        customWhere: api.customWhere ? JSON.parse(api.customWhere) : null,
      };
      if (api.type === 'preset' || api.type === 'customerQuery') {
        convertedAPI.name = api.name;
      } else {
        if (api.fields && api.fields.length) {
          convertedAPI.fields = api.fields.filter(
            field => this._parseBoolean(field.visible)
          ).map(field => field.field_name);
        }

        if (!_.isEmpty(api.filter)) convertedAPI.filter = api.filter;
        let setMap = this._genericPathSetting(api);
        convertedAPI.path = api.path;
        convertedAPI.getSuffix = setMap["getSuffix"];
        convertedAPI.postSuffix = setMap["postSuffix"];
      }
      return convertedAPI;
    });

    const properties = {};
    adapted.fields.forEach(field => {
      const name = field.field_name.replace(/[^a-zA-Z0-9\u4E00-\u9FA5_]/g, '_');
      const type = this._apiDataType2TsType(field.data_type) || 'string';
      const jsonDataType = this._apiDataType2OpenAPIType(field.data_type);
      const property = {
        type,
        id: field.primary_key_position > 0,
        required: this._parseBoolean(field.required),
        originalConfig: field
      };
      properties[name] = property;
      if (field.autoincrement === true || field.autoincrement === 'true' || field.autoincrement === 'YES' || field.autoincrement === 'yes') property.autoincrement = true;
      if (jsonDataType) property.jsonSchema = Object.assign({}, OpenAPIDataTypes[jsonDataType]);
      if (field.field_alias) {
        property.jsonSchema = property.jsonSchema || {};
        property.jsonSchema.title = field.field_alias;
      }
      if (type === 'array') property.itemType = field.itemType;
      if (field.description) property.description = `'${field.description}'`;
      if (!this._idProperty && property.id) {
        this._idProperty = name;
        this._idType = type;
      }
      if (type === 'string' && property.jsonSchema && field.length) property.jsonSchema.length = field.length || 0;
      if (type === 'number' && property.jsonSchema && field.precision) property.jsonSchema.precision = field.precision || 0;
    });

    const name = adapted.prefix + adapted.basePath;
    if (!this._idProperty) {
      log.error(`model ${name} missing primary key`);
      this._valid = false;
      return {};
    }

    return  {
      name,
      title: adapted.description || name,
      tableName: adapted.tablename,
      schema: this._ds.config.settings.schema,
      apiId: adapted.apiId,
      properties,
      dataSourceName: this._ds.getName(),
      dataSourceType: this._ds.getType(),
      originalConfig: adapted,
      pathSetting: this._api.pathSetting
    };
  }

  _generateController () {
    // TODO: generate controller config
    if (!this._model) this._model = this._generateModel();
    const originalModel = this._model.originalConfig;
    if(!originalModel){
      return;
    }
    const apis = {};

    const basePath = originalModel.basePath || this._model.name;
    let apiPrefix = '/api';
    if(originalModel.apiVersion && originalModel.apiVersion !== ''){
      apiPrefix += `/${originalModel.apiVersion}`;
    }else{
      originalModel.apiVersion = '';
    }
    if (originalModel.prefix) apiPrefix += `/${originalModel.prefix}`;
    if (!basePath.startsWith('/')) apiPrefix += '/';
    apiPrefix += basePath;
    let setMap = this._genericPathSetting(this._model);
    originalModel.paths.forEach((item, index) => {
      let name = item.name;
      if (item.type === 'custom') {
        name = `findPage_${index}`;
        if (!item.path || !item.path.trim()) {
          log.error(`missing path for model ${this._model.name} path ${item.name}`);
          return;
        }
      }
      let reqPath;
      if (item.path) {
        reqPath = item.path.startsWith('/') ? item.path : `${apiPrefix}/${item.path}`;
      } else reqPath = apiPrefix;

      const properties = this._model.properties;
      const availableQueryFieldsDescription = item.availableQueryField.map(aqf => {
        const property = properties[aqf];
        const type = property && property.type ? property.type : '';
        return `${aqf} : "${type}"`;
      });
      const requiredQueryFieldsDescription = item.requiredQueryField.map(rqf => {
        const property = properties[rqf];
        const type = property && property.type ? property.type : '';
        return `${rqf} : "${type}"`;
      });
      let sort = [];
      if(item.sort){
        for(let x in item.sort){
          sort.push(item.sort[x].fieldName + ' ' + item.sort[x].type);
        }
      }
      let fieldDoc = {};
      let parametersDoc = [];
      let select = {};
      if(item.select){
        for(let x in item.select){
          let temp = item.select[x];
          select[temp.field_name] = true;
          fieldDoc[item.select[x].field_name] = {
            originalDataType: temp["originalDataType"],
            sourceDbType: temp["sourceDbType"],
            dataType: temp["data_type"],
            defaultValue: temp["default_value"],
            fieldName: temp["field_name"],
            fieldAlias: temp["field_alias"],
          };
        }
      }
      if (item.parameters) {
        for(let x in item.parameters){
          parametersDoc.push({
            name: item.parameters[x].name,
            type: item.parameters[x].type,
            description: item.parameters[x].description,
            required: item.parameters[x].required || false,
            defaultvalue: item.parameters[x].defaultvalue,
            in: "query"
          });
        }
      }
      if(!item.where) {
        item.where = [];
      }
      const api = {
        allPathId: item.allPathId,
        pathTpl: item.pathTpl,
        method: item.method,
        rawName: item.rawName,
        result: item.result,
        description: item.description,
        type: item.type,
        name,
        path: reqPath,
        getSuffix: setMap["getSuffix"],
        postSuffix: setMap["postSuffix"],
        summary: item.description,
        filter: item.filter,
        params: item.params,
        availableQueryField: item.availableQueryField,
        requiredQueryField: item.requiredQueryField,
        availableQueryFieldDescription: availableQueryFieldsDescription.join(','),
        requiredQueryFieldDescription: requiredQueryFieldsDescription.join(','),
        roles: item.roles,
        parameters: item.parameters,
        where: item.where,
        whereString: JSON.stringify(item.where),
        fullCustomQuery: item.fullCustomQuery,
        customWhere: item.customWhere,
        sort: item.sort,
        sortString: JSON.stringify(sort),
        select: item.select,
        selectString: JSON.stringify(select),
        fieldDoc: fieldDoc,
        parametersDoc: parametersDoc
      };
      if (item.fields && item.fields.length) {
        api.fields = {};
        for (let x in item.fields) {
          api.fields[item.fields[x].field_name] = 1;
        }
      }
      apis[name] = api;
    });

    return {
      name: `${this._model.name}_${originalModel.apiVersion}`,
      modelName: this._model.name,
      repositoryName: this._model.name,
      tableName: this._model.tableName,
      apiId: this._model.apiId,
      apiName: originalModel.apiName,
      apiPrefix,
      api: apis,
      idType: this._idType,
      dataSourceName: this._model.dataSourceName,
      dataSourceType: this._model.dataSourceType,
      originalConfig: originalModel,
      dataSource: this._ds,
      config: conf
    };
  }

  _generateRepository () {
    if (!this._model) this._model = this._generateModel();
    return {
      name: this._model.name,
      modelName: this._model.name,
      tableName: this._model.tableName,
      apiId: this._model.apiId,
      dataSourceName: this._model.dataSourceName,
      dataSourceType: this._model.dataSourceType,
      idProperty: this._idProperty,
      originalConfig: this._model.originalConfig
    };
  }

  _genericNameAliasMapping() {
    let nameAliasMapping = {};
    this._api?.paths[0]?.fields.filter(field => {
      return field.field_name && field.field_alias && field.field_alias !== '';
    }).forEach(field => {
      nameAliasMapping[field.field_name] = field.field_alias;
    });
    return nameAliasMapping;
  }
  _convertFields () {
    return this._api.fields.filter(field => {
      return field.field_name && !field.field_name.includes('.');
    }).map(field => {
      const convertedField = {
        field_name: field.field_name,
        field_alias: field.field_alias || field.alias_name || '',
        data_type: this._getFieldType(field),
        primary_key_position: field.primary_key_position,
        autoincrement: field.autoincrement,
      };
      const typeLower = convertedField.data_type.toLowerCase();
      if (['string', 'char'].includes(typeLower)) {
        convertedField.length = field.columnSize;
      }
      if (['number', 'double', 'float'].includes(typeLower)) {
        convertedField.precision = field.precision;
      }
      if (typeLower === 'array') {
        convertedField.itemType = this._apiDataType2TsType(field.itemType) || 'object';
      }
      return convertedField;
    });
  }

  _getFieldType (field) {
    if(this._ds.config && this._ds.config.dataSourceType && this._ds.config.dataSourceType.toLowerCase() !== 'mongodb'){
      if(field.tapType){
        field.tapType = JSON.parse(field.tapType);
        if(['mysql','tidb'].includes(this._ds.config.dataSourceType.toLowerCase())){
          if(field.data_type.includes('bigint')){
            return 'string';
          }
        }
        return TapTypeToDataType[field.tapType.type.toLocaleString()];
      }
    }
    if (field.node_data_type) return field.node_data_type;
    const javaType = field.javaType || field.java_type;
    if (javaType && javaType !== 'Unsupported') return javaType;
    if (field.data_type) return field.data_type;
    return 'String';
  }

  _apiDataType2TsType (apiType) {
    return this._apiDataType2Type(apiType, TsType2APITypes);
  }

  _apiDataType2OpenAPIType (apiType) {
    return this._apiDataType2Type(apiType, OpenAPIType2APITypes);
  }

  _apiDataType2Type (apiType, map) {
    if (!apiType) return null;
    let lower = apiType.toLowerCase().replace(/_/g,"");
    const pair = Object.entries(map).find(([k, v]) => v.includes(lower));
    return pair ? pair[0] : null;
  }

  _parseBoolean (flag) {
    return flag === true || flag === 'true';
  }

  _genericPathSetting(api) {
    let setMap = {
      "getSuffix": "",
      "postSuffix": "/find"
    };
    log.info("path: " + JSON.stringify(api))
    api.pathSetting.forEach(item => {
      if (!item.path || item.path === '' ) {
        return;
      }
      let path = item.path.startsWith('/') ? item.path : `/${item.path}`;
      switch (item.type) {
        case 'DEFAULT_POST':
          setMap["postSuffix"] = path;
          break;
        case 'DEFAULT_GET':
          setMap["getSuffix"] = path;
          break;
      }
    });
    return setMap;
  }
}

module.exports = CRUDAPI;
