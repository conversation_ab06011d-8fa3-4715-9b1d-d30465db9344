/**
 * <AUTHOR>
 * @date 1/8/20
 * @description
 */

let testGeneratorExcel = function(fields) {

	let modelName = "Logs";

	/*const xl = require('excel4node');
	const wb = new xl.Workbook();
	const ws = wb.addWorksheet(`${modelName} records`);

	let rowIdx=1, cellIdx = 1;

	// header
	fields.forEach(v => ws.cell(rowIdx, cellIdx++).string(v) );

	ws.row(rowIdx).freeze();

	rowIdx++;

	console.log(ws.opts)

	wb.write('/home/<USER>/temp/test.xlsx');*/
	let Excel = require('exceljs');
	let wb = new Excel.Workbook();

	let ws = wb.addWorksheet(`${modelName} records`);
	let rowIdx=1, cellIdx = 1;

	// header
	ws.addRow(fields);
	ws.addRow(fields);
	ws.addRow(fields);

	ws.views = [
		{state: 'frozen', xSplit: 0, ySplit: 1}
	];

	wb.xlsx.writeFile('/home/<USER>/temp/test.xlsx').then(() => {
		console.log('success')
	});

};

//testGeneratorExcel(['test', 'test1', 'test2', 'test3']);


let testReadExcel = function(){
	let Excel = require('exceljs');
	let wb = new Excel.Workbook();

	let fs = require('fs');
	let buffer = fs.readFileSync('/home/<USER>/temp/test.xlsx');
	wb.xlsx.load(buffer).then(() => {
		let ws = wb.getWorksheet(1);
		ws.eachRow( row => {
			row.eachCell(cell => {
				console.log(cell.value);
			});
		});
	});

};
//testReadExcel();

let testUploadExcel = function(){
	const fs = require('fs');
	const request = require('request');
	let file = "/home/<USER>/temp/Logs.xlsx";
	let formData = {
		field: 'field_value',
		file: [{
			value: fs.createReadStream(file ),
			options: {
				filename: '/home/<USER>/workspace/nodejs/apig/reportApiCallStatsBatchLogic.js',
				contentType: 'application/javascript'
			}
		},{
			value: fs.createReadStream(file),
			options: {
				filename: '/home/<USER>/workspace/nodejs/apig/package.json',
				contentType: 'application/json'
			}
		}],
		file1: {
			value: fs.createReadStream(file),
			options: {
				filename: '/home/<USER>/workspace/nodejs/apig/index.ts',
				contentType: 'application/typescript'
			}
		}
	};

	request.post({
		url: 'http://**************:3080/api/v1/Student',
		headers: {
			access_token: '***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
		},
		formData: formData
	}, (err, httpResponse, body) => {
		if (err) {
			return console.error('upload failed:', err);
		}
		console.log('Upload successful!  Server responded with:', body);
	})

};

testUploadExcel();

let testImportExcel = function() {
	let importExcel = require('../dist/src/importExcel').importExcel;
	importExcel.readFromFiles(['/home/<USER>/temp/Logs.xlsx']).then( results => {
		console.log(JSON.stringify(results, '', '\t'));
	})
};

//testImportExcel();
