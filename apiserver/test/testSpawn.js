/**
 * <AUTHOR>
 * @date 12/25/19
 * @description
 */
const cp = require('child_process'),
	path = require('path');

let startApiServer = function(){
	let apiServerHome = path.join(path.dirname(process.argv[1]), '../');

	console.log(`Start Api Server`);
	console.log(`Api Server HOME: ${apiServerHome}`);

	let subProcess = cp.spawn('node', [ 'index.js' ], {
		cwd: apiServerHome,

		env: Object.assign({
			API_SERVER_PORT: 3081
		}, process.env),

		stdio: 'ignore',
		detached: true
	});
	subProcess.unref();

	console.log('API Server started.');
};


let startAgent = function(){
	let agentHome = path.join(path.dirname(process.argv[1]), '../../daas/connector/');

	console.log(`Start agent`);
	console.log(`Agent HOME: ${agentHome}`);

	let subProcess = cp.spawn('java', [ '-jar', 'connector-manager.jar' ], {
		cwd: agentHome,

		env: Object.assign({
			// API_SERVER_PORT: 3081
		}, process.env),

		stdio: 'ignore',
		detached: true
	});
	subProcess.unref();

	console.log('Agent started.');
};

startAgent();
