'use strict';

const LBDataSource = require('loopback-datasource-juggler').DataSource;

const log = require('../dist').log.app;

class DataSource {

  constructor (connection, dataSourceType) {
    this.config = {
      id: connection.id,
      name: connection.name || `_${connection.id}`,
      dataSourceType,
      settings: {
        connector: dataSourceType,
        ssl: connection.ssl || false,
        sslKey: connection.sslKey,
        sslCert: connection.sslCert,
        sslPass: connection.sslPass,
        sslValidate: connection.sslValidate,
        sslCA: connection.sslCA ? [connection.sslCA] : [],
        checkServerIdentity: connection.checkServerIdentity,
        readPreference: connection.readPreference,
        readConcern: connection.readConcern
      }
    };
    if (connection.database_uri) {
      this.config.settings.url = decodeURI(connection.database_uri);
    } else {
      Object.assign(this.config.settings, {
        host: connection.database_host,
        port: connection.database_port,
        user: connection.database_username,
        password: connection.database_password,
        database: connection.database_name,
        schema: connection.database_owner
      });
    }
  }

  getId () {
    return this.config.id;
  }

  getName () {
    return this.config.name;
  }

  getType () {
    return this.config.dataSourceType;
  }

  getConfig () {
    return this.config;
  }

  testConnection () {
    const settings = this.config.settings;
    const uri = settings.url || `${settings.connector}://${settings.host}:${settings.port}/${settings.database}`;
    return new Promise(resolve => {
      log.info(`testing connection to ${uri}...`);
      const ds = new LBDataSource(this.config.settings);
      ds.on('error', err => {
        log.error(`failed to connect to ${uri}`, err);
        return resolve(false);
      });
      ds.ping(function (err) {
        if (err) {
          log.error(`failed to ping ${uri}`, err);
          return resolve(false);
        }
        log.info(`established connection to ${uri}`);
        ds.disconnect(function (err) {
          if (err) {
            log.error(`failed to disconnect from ${uri}`, err);
            return resolve(false);
          }
          log.info(`disconnected from ${uri}`);
          return resolve(true);
        });
      });
    });
  }
}

module.exports = DataSource;
