const fs = require('fs');
const { execSync } = require('child_process');
const client = require('prom-client');

const openFileDescriptorsMeter = (register) => {
  return new client.Gauge({
    name: 'process_files_open_files',
    help: 'Number of open file descriptors/handles by the process',
    labelNames: ['worker_id'],
    registers: [register],
    collect() {
      // 获取当前进程的文件句柄数
      const openFiles = getOpenFileDescriptors();
      this.set({ worker_id: process.pid.toString() }, openFiles);
    },
  });
}

// 获取进程打开的文件描述符数量
const getOpenFileDescriptors = () => {
  try {
    if (process.platform === 'linux') {
      // Linux: 读取 /proc/self/fd 目录
      const fds = fs.readdirSync('/proc/self/fd');
      return fds.length;
    } else if (process.platform === 'darwin') {
      // macOS: 使用 lsof 命令
      const output = execSync(`lsof -p ${process.pid} | wc -l`, { encoding: 'utf8' });
      // lsof 输出包含标题行，所以减1
      return Math.max(0, parseInt(output.trim()) - 1);
    } else if (process.platform === 'win32') {
      // Windows: 使用 process._getActiveHandles() 和 process._getActiveRequests()
      const handles = process._getActiveHandles ? process._getActiveHandles().length : 0;
      const requests = process._getActiveRequests ? process._getActiveRequests().length : 0;
      return handles + requests;
    } else {
      // 其他平台：返回一个估算值
      const handles = process._getActiveHandles ? process._getActiveHandles().length : 0;
      const requests = process._getActiveRequests ? process._getActiveRequests().length : 0;
      return handles + requests;
    }
  } catch (error) {
    // 如果获取失败，返回0或使用备用方法
    console.warn('Failed to get open file descriptors:', error.message);
    try {
      const handles = process._getActiveHandles ? process._getActiveHandles().length : 0;
      const requests = process._getActiveRequests ? process._getActiveRequests().length : 0;
      return handles + requests;
    } catch {
      return 0;
    }
  }
}

module.exports = {
  openFileDescriptorsMeter
}