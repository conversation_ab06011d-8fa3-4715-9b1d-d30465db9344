const cluster = require('cluster');
const client = require('prom-client');
const {openFileDescriptorsMeter} = require('./metrics');

const REQUEST_AGGREGATED_METRICS = 'request-aggregated-metrics'
const AGGREGATED_METRICS_RESPONSE = 'aggregated-metrics-response'

let aggregatorRegistry;
let registry;

const initAggregatorRegistry = () => {
  console.log(`Init aggregator registry in master process(${process.pid}).`)
  aggregatorRegistry = new client.AggregatorRegistry();
  cluster.on('online', (worker) => {
    worker.on('message', (message) => {
      if (message.type === REQUEST_AGGREGATED_METRICS) { // Worker请求聚合指标
        getAggregatedMetrics().then(metrics => {
          console.log('Receive aggregate metrics: ', metrics.length);
          worker.send({
            type: AGGREGATED_METRICS_RESPONSE,
            requestId: message.requestId,
            metrics: metrics,
            success: true
          });
        }).catch(error => {
          console.error('Failed to get aggregated metrics:', error);
          worker.send({
            type: AGGREGATED_METRICS_RESPONSE,
            requestId: message.requestId,
            metrics: `# Error getting aggregated metrics: ${error.message}\n`,
            success: false
          });
        });
      }
    })
  });
  cluster.on('disconnect', (worker) => {
    worker.removeAllListeners('message')
  })
}

const initRegistry = () => {
  if (cluster.isMaster) {
    initAggregatorRegistry()
  }

  console.log(`Init registry in ${cluster.isMaster ? 'master' : 'child'} process(${process.pid}).`)
  registry = new client.Registry();
  client.collectDefaultMetrics({
    registry,
    labels: {
      worker_id: process.pid.toString()
    }
  });
  client.AggregatorRegistry.setRegistries([registry]);
  openFileDescriptorsMeter(registry);
}

const getAggregatedMetrics = cluster.isMaster ? async function() {
  // 聚合所有worker指标的函数
  if (!aggregatorRegistry)
    return '# Aggregate metrics not initialized in master process';
  try {
    console.log('Get aggregated metrics in master process ' + process.pid);
    const metrics = await aggregatorRegistry.clusterMetrics() || '# No metrics available\n';

    console.log(metrics)
    return metrics
  } catch (error) {
    console.warn('Failed to get aggregated metrics:', error.message);
    return `# Error getting aggregated metrics: ${error.message}`;
  }
} : async function() {
  return new Promise((resolve, reject) => {

    console.log('Get aggregated metrics in child process ' + process.pid);

    const requestId = Date.now() + Math.random();
    const timeout = setTimeout(() => {
      reject(new Error('Timeout waiting for aggregated metrics from master'));
    }, 15000);

    // 监听master的响应
    const messageHandler = (message) => {
      if (message.type === AGGREGATED_METRICS_RESPONSE && message.requestId === requestId) {
        clearTimeout(timeout);
        process.removeListener('message', messageHandler);

        console.log(`Receive aggregated metrics for request: ${message.requestId}, result: ${message.success}`);
        if (message.success) {
          resolve(message.metrics);
        } else {
          reject(new Error(`Master failed to get metrics: ${message.metrics}`));
        }
      }
    };

    process.on('message', messageHandler);
    process.send({
      type: REQUEST_AGGREGATED_METRICS,
      requestId: requestId
    });
  });
}

const getMetrics = async () => {
  try {
    return await registry.metrics();
  } catch (error) {
    console.error(`Worker ${process.pid} failed to get metrics:`, error);
    return `# Worker ${process.pid} failed to get metrics: ${ error}`
  }
}

module.exports = {
  initRegistry,
  getAggregatedMetrics,
  getMetrics,
  getContentType: () => client.contentType
}