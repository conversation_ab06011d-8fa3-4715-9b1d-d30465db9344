const cluster = require('cluster');
const client = require('prom-client');
const {openFileDescriptorsMeter} = require('./metrics');

const REQUEST_AGGREGATED_METRICS = 'request-aggregated-metrics'
const AGGREGATED_METRICS_RESPONSE = 'aggregated-metrics-response'

let aggregatorRegistry;
let registry;

const initAggregatorRegistry = () => {
  console.log(`Init aggregator registry in master process(${process.pid}).`)
  aggregatorRegistry = new client.AggregatorRegistry();

  // 监听已存在的workers
  Object.values(cluster.workers || {}).forEach(worker => {
    if (worker) {
      setupWorkerMessageHandler(worker);
    }
  });

  cluster.on('online', (worker) => {
    console.log(`Worker ${worker.process.pid} came online`);
    setupWorkerMessageHandler(worker);
  });

  cluster.on('disconnect', (worker) => {
    console.log(`Worker ${worker.process.pid} disconnected`);
    worker.removeAllListeners('message')
  })
}

const setupWorkerMessageHandler = (worker) => {
  worker.on('message', (message) => {
    if (message.type === REQUEST_AGGREGATED_METRICS) { // Worker请求聚合指标
      console.log(`Received metrics request from worker ${worker.process.pid}, requestId: ${message.requestId}`);
      getAggregatedMetrics().then(metrics => {
        console.log('Sending aggregated metrics, length: ', metrics.length);
        worker.send({
          type: AGGREGATED_METRICS_RESPONSE,
          requestId: message.requestId,
          metrics: metrics,
          success: true
        });
      }).catch(error => {
        console.error('Failed to get aggregated metrics:', error);
        worker.send({
          type: AGGREGATED_METRICS_RESPONSE,
          requestId: message.requestId,
          metrics: `# Error getting aggregated metrics: ${error.message}\n`,
          success: false
        });
      });
    }
  })
}

const initRegistry = () => {
  console.log(`Init registry in ${cluster.isMaster ? 'master' : 'child'} process(${process.pid}).`)

  // Master进程：初始化收集worker进程监控指标
  if (cluster.isMaster) {
    initAggregatorRegistry();
    console.log(`Master process ${process.pid}: AggregatorRegistry initialized`);
  }
  // Master Worker进程都需要采集监控指标：创建自己的registry并收集指标
  registry = new client.Registry();
  client.collectDefaultMetrics({
    registry,
    labels: {
      worker_id: process.pid.toString()
    }
  });

  client.AggregatorRegistry.setRegistries([registry]);
  openFileDescriptorsMeter(registry);

  console.log(`Worker process ${process.pid}: Registry initialized and registered for aggregation`);
}

const getAggregatedMetrics = cluster.isMaster ? async function() {
  // 主进程：聚合所有worker指标
  if (!aggregatorRegistry) {
    console.warn('AggregatorRegistry not initialized in master process');
    return '# Aggregate metrics not initialized in master process';
  }
  try {
    console.log('Getting aggregated metrics in master process ' + process.pid);
    const metrics = await aggregatorRegistry.clusterMetrics();
    if (!metrics) {
      console.warn('No metrics returned from clusterMetrics()');
      return '# No metrics available\n';
    }
    console.log(`Aggregated metrics length: ${metrics.length}`);
    return metrics;
  } catch (error) {
    console.error('Failed to get aggregated metrics:', error);
    return `# Error getting aggregated metrics: ${error.message}`;
  }
} : async function() {
  // Worker进程：请求主进程的聚合指标
  return new Promise((resolve, reject) => {
    console.log('Requesting aggregated metrics from master, worker process ' + process.pid);

    const requestId = Date.now() + Math.random();
    const timeout = setTimeout(() => {
      console.error(`Timeout waiting for aggregated metrics from master, requestId: ${requestId}`);
      reject(new Error('Timeout waiting for aggregated metrics from master'));
    }, 10000); // 减少超时时间到10秒

    // 监听master的响应
    const messageHandler = (message) => {
      if (message.type === AGGREGATED_METRICS_RESPONSE && message.requestId === requestId) {
        clearTimeout(timeout);
        process.removeListener('message', messageHandler);

        console.log(`Received aggregated metrics for request: ${message.requestId}, success: ${message.success}`);
        if (message.success) {
          resolve(message.metrics);
        } else {
          reject(new Error(`Master failed to get metrics: ${message.metrics}`));
        }
      }
    };

    process.on('message', messageHandler);

    // 检查是否可以发送消息
    if (typeof process.send === 'function') {
      console.log(`Sending metrics request to master, requestId: ${requestId}`);
      process.send({
        type: REQUEST_AGGREGATED_METRICS,
        requestId: requestId
      });
    } else {
      clearTimeout(timeout);
      reject(new Error('Cannot send message to master process'));
    }
  });
}

const getMetrics = async () => {
  try {
    if (!registry) {
      return `# No registry available in process ${process.pid} (${cluster.isMaster ? 'master' : 'worker'})`;
    }
    return await registry.metrics();
  } catch (error) {
    console.error(`Process ${process.pid} failed to get metrics:`, error);
    return `# Process ${process.pid} failed to get metrics: ${error.message}`
  }
}

module.exports = {
  initRegistry,
  getAggregatedMetrics,
  getMetrics,
  getContentType: () => client.contentType
}