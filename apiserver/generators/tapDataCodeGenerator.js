'use strict';

const dataSourceFactory = require('../dataSources');
const apiFactory = require('../apis');

const ModelGenerator = require('./model');
const DataSourceGenerator = require('./datasource');
const RepositoryGenerator = require('./repository');
const ControllerGenerator = require('./controller');
const deleteTs = require('./delete-ts');
const build = require('./build');
const log = require('../dist').log.app;

class TapDataCodeGenerator {

  constructor ({ connections, apis }) {
    this.dataSources = connections ? connections.map(connection => dataSourceFactory.createDataSource(connection)) : [];
    this.apis = [];
    if(apis) {
      apis.forEach(apiConfig => {
        const dataSource = this.dataSources.find(dataSource => dataSource.getId() === apiConfig.datasource);
        if (!dataSource) {
          log.warn(`can't find connection for api ${apiConfig.id}, ignoring it...`);
          return;
        }
        this.apis.push(apiFactory.createAPI(dataSource, apiConfig));
      });
    }
  }

  async generate () {
    log.info('filtering unavailable connections and related apis...');
    await this._filterUnavailable();

    log.info('deleting current apis source code...');
    await (new Promise(resolve => deleteTs(resolve)));

    log.info('generating new apis source code...');
    const promises = [];
    // data source code generation
    this.dataSources.forEach(ds => {
      promises.push(new Promise(resolve => new DataSourceGenerator(ds.getConfig()).on('done', resolve)));
    });
    // model, repository and controller source code generation
    this.apis.filter(api => api.isValid()).forEach(api => {
      promises.push(new Promise(resolve => new ModelGenerator(api.getModel()).on('done', resolve)));
      promises.push(new Promise(resolve => new RepositoryGenerator(api.getRepository()).on('done', resolve)));
      promises.push(new Promise(resolve => new ControllerGenerator(api.getController()).on('done', resolve)));
    });
    await Promise.all(promises);

    log.info('new api source code generated, building...');
    if (!build()) throw new Error('build failed');
    log.info('build completed');
  }

  async _filterUnavailable () {
    const testResults = await Promise.all(this.dataSources.map(ds => ds.testConnection()));
    this.dataSources = this.dataSources.filter((ds, i) => testResults[i]);
    this.apis = this.apis.filter(api => this.dataSources.some(ds => api.getDataSource().getId() === ds.getId()));
  }
}

module.exports = TapDataCodeGenerator;
