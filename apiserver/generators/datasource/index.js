const Generator = require('../generator');
const utils = require('../utils');

/**
 * 数据源生成器
 */
class DataSourceGenerator extends Generator {
	constructor(dataSourceConfig) {
		super(dataSourceConfig);

		this.artifactInfo = Object.assign({
			type: 'datasource',
			rootDir: this.destinationRoot(),
			outDir: utils.datasourcesDir,
		}, dataSourceConfig);

		this.artifactInfo.className = utils.toClassName(this.artifactInfo.name);
		this.artifactInfo.fileName = utils.kebabCase(this.artifactInfo.name);
		this.artifactInfo.jsonFileName = `${this.artifactInfo.fileName}.datasource.json`;
		this.artifactInfo.outFile = utils.getDataSourceFileName(this.artifactInfo.name);

		// Resolved Output Paths.
		const jsonPath = this.destinationPath(
			this.artifactInfo.outDir,
			this.artifactInfo.jsonFileName,
		);
		const tsPath = this.destinationPath(
			this.artifactInfo.outDir,
			this.artifactInfo.outFile,
		);

		const ds = Object.assign({
			"name": this.artifactInfo.name,
			"connector": this.artifactInfo.dataSourceType,
			"allowExtendedOperators": true,
			"useNewUrlParser": true,
		}, this.artifactInfo.settings);
		if(['mysql','tidb'].includes(this.artifactInfo.dataSourceType.toLowerCase())){
			ds.dateStrings= true;
			ds.supportBigNumbers= true;
			ds.bigNumberStrings= true;
		}
		// Copy Templates
		this.fs.writeJSON(jsonPath, ds);
		this.copyTemplateTpl(this.templatePath(), tsPath, this.artifactInfo);
		this.log.info(`generator data source config ${jsonPath}`);
		this.log.info(`generator data source ${tsPath}`);
	}
}

module.exports = DataSourceGenerator;
