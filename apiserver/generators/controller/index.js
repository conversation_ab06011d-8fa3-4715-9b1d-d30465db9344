const Generator = require('../generator');
const utils = require('../utils');

/**
 * controller 生成器
 */
class ControllerGenerator extends Generator {

	constructor(config) {
		super(config);

		this.artifactInfo = Object.assign({
			type: 'controller',
			rootDir: this.destinationRoot(),
			outDir: utils.controllersDir
		}, config);

		this.artifactInfo.className = utils.toClassName(this.artifactInfo.name);
		this.artifactInfo.modelName = utils.toClassName(this.artifactInfo.modelName || this.artifactInfo.name);
		this.artifactInfo.repositoryName = utils.toClassName(this.artifactInfo.repositoryName || this.artifactInfo.name) + 'Repository';
		this.artifactInfo.modelVariableName = '_' + utils.camelCase(this.artifactInfo.modelName);
		this.artifactInfo.repositoryNameCamel = '_' + utils.camelCase(this.artifactInfo.repositoryName);
		this.artifactInfo.outFile = utils.getControllerFileName(this.artifactInfo.name);

		const tsPath = this.destinationPath(
			this.artifactInfo.outDir,
			this.artifactInfo.outFile
		);

		this.copyTemplateTpl(this.templatePath(), tsPath, this.artifactInfo);
		this.log.info(`generator controller ${tsPath}`);
	}
}

module.exports = ControllerGenerator;
