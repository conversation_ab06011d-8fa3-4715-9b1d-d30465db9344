// moved from ../controller.ts.ejs
@authenticate('JwtStrategy', {
    allPathId:'<%= val['allPathId'] %>',
    rawName:'<%= val['rawName'] %>',
    result:'<%= val['result'] %>',
    description:'<%= val['description'] %>',
    type:'<%= val['type'] %>',
    pathTpl:'<%= val['pathTpl'] %>',
    roles: <%- JSON.stringify(val['roles'])%>,
    originalConfig: <%- JSON.stringify(originalConfig)%>,
    originalConfig: <%- JSON.stringify(originalConfig)%>
  })
@post('<%= val['path'] %>', {
  summary: '<%= val['summary']%>',
  tags:['<%= name %>'],
  responses: {
    '200': {
      description: '<%= modelName %> model instance',
      content: {'application/json': {schema: {'x-ts-type': <%= modelName %>}}},
    },
  },
  "x-table-name": "<%=tableName%>",
  "x-api-id": "<%=apiId%>",
  "x-api-name": "<%=apiName%>",
  "x-bsm-operation": "<%= modelName %>.create"
})
async create(@requestBody() <%= modelVariableName %>: <%= modelName %>): Promise<<%= modelName %>> {
  return await this.<%= repositoryNameCamel %>.create(<%= modelVariableName %>);
}


@authenticate('JwtStrategy', {
    allPathId:'<%= val['allPathId'] %>',
    rawName:'getBatchTemplate',
    result:'Document',
    description:'Download batch import excel template',
    type:'preset',
    pathTpl:'<%= val['pathTpl'] %>/batch',
    roles: <%- JSON.stringify(val['roles'])%>,
    originalConfig: <%- JSON.stringify(originalConfig)%>
})
@get('<%= val['pathTpl'] %>/batch', {
    summary: 'Download batch import excel template',
    tags:['<%= name %>'],
    responses: {
        '200': {
            description: 'Download batch import excel template',
            content: {'application/octet-stream': {}},
        },
    },
    "x-table-name": "<%=tableName%>",
    "x-api-id": "<%=apiId%>",
    "x-api-name": "<%=apiName%>",
    "x-bsm-operation": "<%= modelName %>.getBatchTemplate"
})
async getBatchTemplate(@inject(RestBindings.Http.CONTEXT) ctx: RequestContext): Promise<object> {

    let properties = this.<%= repositoryNameCamel %>.entityClass.definition.properties;
    let modelName = this.<%= repositoryNameCamel %>.entityClass.definition.name;
    let fields = Object.keys(properties).map(function(name){
    // @ts-ignore
    let title = properties[name].jsonSchema && properties[name].jsonSchema.title || '';
        return title ? `${title}(${name})` : name;
    });
    ctx.response.setHeader('Content-Disposition', 'attachment; filename="' + modelName + '.xlsx"');
    return importExcel.generatorTemplate([fields], `${modelName} records`);
}

@authenticate('JwtStrategy', {
    allPathId:'<%= val['allPathId'] %>',
    rawName:'getBatchTemplate',
    result:'Document',
    description:'Batch import excel records',
    type:'preset',
    pathTpl:'<%= val['pathTpl'] %>/batch',
    roles: <%- JSON.stringify(val['roles'])%>,
    originalConfig: <%- JSON.stringify(originalConfig)%>
})
@post('<%= val['pathTpl'] %>/batch', {
    summary: 'Batch import excel records',
    tags:['<%= name %>'],
    responses: {
    '200': {
    description: 'Batch import excel records',
        content: {'application/json': {
            schema: {type: 'object', properties: {
                total: { type: 'number' },
                    details: { type: 'object'}
                }},
            }},
        },
    },
    "x-table-name": "<%=tableName%>",
    "x-api-id": "<%=apiId%>",
    "x-api-name": "<%=apiName%>",
    "x-bsm-operation": "<%= modelName %>.batchImport"
})
async batchImport(
    @requestBody({
        description: 'multipart/form-data value.',
        required: true,
        content: {
            'multipart/form-data': {
                'x-parser': 'stream',
                schema: {type: 'object'},
            },
        },
    })
    request: Request,
@inject(RestBindings.Http.CONTEXT)
ctx: RequestContext): Promise<{total: 0, details?: {}}> {
    let tmpdir = require('path').join(require('os').tmpdir(), 'api-server');
    const multer = require('multer');
    const upload = multer({
        dest: tmpdir,
        limits: {
            fileSize: 1024*1024*100
        },
        fileFilter: (req: any, file: any, cb: any) => {
            let originalName = file.originalname || '';
            if( originalName.lastIndexOf('.xlsx') >= 0 ) {
                cb(null, true);
            } else {
                cb(null, false);
            }
        }
    });

    return new Promise<{total: 0}>((resolve, reject) => {
        upload.any()(request, ctx.response, (err: any) => {
            if( err ){
                reject(err);
            } else {
                // @ts-ignore
                let files: any[] = request.files;
                let filePaths = files.map( file => file.path);
                importExcel.readFromFiles(filePaths).then((result) => {
                    let total: number = 0;
                    let promises: Promise<<%= modelName %>>[] = [];
                    Object.keys(result).forEach(file => {
                        // @ts-ignore
                        Object.keys(result[file]).forEach(sheet => {
                            // @ts-ignore
                            total += result[file][sheet].length;
                            // @ts-ignore
                            promises.push(this.<%= repositoryNameCamel %>.createAll(result[file][sheet]));
                        });
                    });
                    Promise.all(promises).then((results => {
                        // @ts-ignore
                        resolve({ total: total })
                    })).catch(reject);
                }).catch(reject);
            }
        });
    });
}