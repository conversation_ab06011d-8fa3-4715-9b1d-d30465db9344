// moved from ../controller.ts.ejs
@authenticate('JwtStrategy', {
    allPathId:'<%= val['allPathId'] %>',
    rawName:'<%= val['rawName'] %>',
    result:'<%= val['result'] %>',
    description:'<%= val['description'] %>',
    type:'<%= val['type'] %>',
    pathTpl:'<%= val['pathTpl'] %>',
    roles: <%- JSON.stringify(val['roles'])%>,
    originalConfig: <%- JSON.stringify(originalConfig)%>
  })
@get('<%= val['path'] %><%= val['getSuffix'] %>', {
  summary: '<%= val['summary']%>',
  tags:['<%= name %>'],
    responses: {
      '200': {
          description: 'file stream',
          content: {'application/octet-stream ': {}},
        },
        '404': {
            description: 'file not found'
        },
  },
  "x-table-name": "<%=tableName%>",
  "x-api-id": "<%=apiId%>",
  "x-api-name": "<%=apiName%>",
  "x-bsm-operation": "<%= modelName %>.download"
})
async download(
  @inject(RestBindings.Http.RESPONSE) res: Response,
  @param.query.object('filter', getFilterSchemaFor(<%= modelName %>)) filter?: Filter<<%= modelName %>>,
): Promise<any> {
                            // @ts-ignore
                            res.__fun ="download";
  let files = await this.<%= repositoryNameCamel %>.find(filter);
  let file : any = files && files.length > 0 ? files[0] : null;
  if( file ) {
    let filename = file.filename ? file.filename.split('/') : ['file'];
    filename = filename[filename.length - 1]
    if ( file.metadata && file.metadata.file_name) {
      filename = file.metadata.file_name
    }
    if ( file.metadata && file.metadata.file_extension) {
      filename = `${filename}.${file.metadata.file_extension}`
    }
    return {
      filename: encodeURIComponent(filename),
      stream: await this.<%= repositoryNameCamel %>.downloadById(file._id || '')
    };
  } else {
    throw new HttpErrors.NotFound("File not found");
  }
}
