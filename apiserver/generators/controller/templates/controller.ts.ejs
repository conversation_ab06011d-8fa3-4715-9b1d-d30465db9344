import {Count, CountSchema, Filter, repository, Where} from '@loopback/repository';
import {
  post,
  param,
  get,
  getFilterSchemaFor,
  getWhereSchemaFor,
  patch,
  del,
  requestBody,
  HttpErrors,
  RestBindings,
  RequestContext, Request,Response
} from '@loopback/rest';
import {<%= modelName %>} from '../models';
import {<%= repositoryName %>} from '../repositories';
import {
  AuthenticationBindings,
  UserProfile,
  authenticate,
} from '@loopback/authentication';
import {inject,intercept,InvocationContext,ValueOrPromise,InvocationResult,Interceptor} from '@loopback/context';
import {importExcel} from '../importExcel';
import {log} from '../log';
const Conf = require('conf');
const config = new Conf();
const defaultPageLimit = 20;
const logger: Interceptor = async (invocationCtx:any, next:any) => {
    const recv_timestmap = new Date().getTime();
    let result = await next();
    if (!Array.isArray(result)) {
        const resp_timestmap = new Date().getTime();
        if (!result) {
            result={};
        }
        result.api_monit_info = {};
        result.api_monit_info.recv_timestmap = recv_timestmap;
        result.api_monit_info.db_request_exhaust = resp_timestmap - recv_timestmap;
        result.api_monit_info.resp_timestmap = resp_timestmap;
        if (result.data) {
            result.data = js_traverse(result.data);
        } else if (invocationCtx.methodName.indexOf('download') < 0) {
            result = js_traverse(result);
        }
    }
    return result;
};

@intercept(logger)
export class <%= className %>Controller {

  constructor(
    @repository(<%= repositoryName %>)
    public <%= repositoryNameCamel %> : <%= repositoryName %>,
  ) {}

  <%_ Object.entries(api).forEach(([path, val]) => { -%>
    <%_ if (val['type'] === 'preset') { -%>
        <%_ if (val['name'] === 'create') { -%>
        <%- include('./partials/create.ejs', {val, modelName, repositoryNameCamel, modelVariableName, name, tableName, apiId, apiName, originalConfig}) %>
        <%_ } else if (val['name'] === 'findPage') { -%>
        <%- include('./partials/findPage.ejs', {val, modelName, repositoryNameCamel, name, tableName, apiId, apiName, originalConfig}) %>
        <%_ } else if (val['name'] === 'findById') { -%>
        <%- include('./partials/findById.ejs', {val, modelName, repositoryNameCamel, name, tableName, apiId, apiName, originalConfig, idType}) %>
        <%_ } else if (val['name'] === 'updateById') { -%>
        <%- include('./partials/updateById.ejs', {val, modelName, repositoryNameCamel, modelVariableName, name, tableName, apiId, apiName, originalConfig, idType}) %>
        <%_ } else if (val['name'] === 'deleteById') { -%>
        <%- include('./partials/deleteById.ejs', {val, modelName, repositoryNameCamel, name, tableName, apiId, apiName, originalConfig, idType}) %>
        <%_ } else if (val['name'] === 'downloadById') { -%>
        <%- include('./partials/downloadById.ejs', {val, modelName, repositoryNameCamel, name, tableName, apiId, apiName, originalConfig, idType}) %>
        <%_ } else if (val['name'] === 'download') { -%>
        <%- include('./partials/download.ejs', {val, modelName, repositoryNameCamel, name, tableName, apiId, apiName, originalConfig}) %>
        <%_ } else if (val['name'] === 'upload') { -%>
        <%- include('./partials/upload.ejs', {val, modelName, repositoryNameCamel, name, tableName, apiId, apiName, originalConfig}) %>
        <%_ }  %>
    <%_ } else if (val['name'] === 'customerQuery') { -%>
    <%- include('./partials/customerQuery.ejs', {val, modelName, repositoryNameCamel, name, tableName, apiId, apiName, originalConfig}) %>
    <%_ } else if (val['method'] === 'STREAM') { -%>
    <%- include('./partials/stream.ejs', {val, modelName, repositoryNameCamel, name, tableName, apiId, apiName, originalConfig, dataSource}) %>
    <%_ } else { -%>
    <%- include('./partials/custom.ejs', {val, modelName, repositoryNameCamel, name, tableName, apiId, apiName, originalConfig}) %>
    <%_ } -%>
  <%_ }) -%>

  // @ts-ignore
  findAllField(where, allField) {
    if (!where || !allField) { return }
    const oa = where.or || where.and;
    if (oa && Object.prototype.toString.call(oa) === '[object Array]') {
      oa.forEach((item:any) => {
        this.findAllField(item, allField);
      });
    } else {
      for (const key in where) {
        if (key && where.hasOwnProperty(key)) {
          allField.push(key);
        }
      }
    }
  }
}

function js_traverse(o:any) {
  let type = typeof o
  if (type === "object") {
    for (let key in o) {
        let itemType = typeof o[key];
        if (itemType === "object" && o[key] && "Decimal128" === o[key]._bsontype) {
         o[key] = o[key].toString();
         continue;
        }
      else if(key !== '__proto__' && o.hasOwnProperty(key)){
        try{
          o[key] = js_traverse(o[key])
        }catch(e){
          console.error(e);
        }
      }
    }
  } else {
  }
  return o;
}

function filterFields(filterObject: any , assignFunction: (input: Record<string, any>) => void) : Record<string, any> {
    // 确保 filterObject 不为空
    if (!filterObject) {
        filterObject = {};
    }
    //以全局配置为最大查询范围
    let globalFields: Record<string, any> = {};
    assignFunction(globalFields);
    let globalKeys: string[] = [];
    let global: Record<string, boolean> = {};
    Object.keys(globalFields).forEach(key => {
        let value: any = globalFields[key];
        if (value === "true" || value === "1" || value === true || value === 1) {
            globalKeys.push(key);
            global[key] = true;
        }
    });

    let queryFields: Record<string, boolean> = {};
    if( typeof filterObject.fields === 'object' && !Array.isArray(filterObject.fields) && Object.keys(filterObject.fields).length > 0){
        //格式一： {'f1':true, 'f2':1}
        const fieldsObj = filterObject.fields as Record<string, any>;
        Object.keys(fieldsObj).forEach(key => {
            if (globalKeys.indexOf(key) !== -1) {
                let value: any = fieldsObj[key];
                queryFields[key] = (value === "true" || value === "1" || value === true || value === 1);
            }
        });
    } else if (Array.isArray(filterObject.fields) && filterObject.fields.length > 0) {
        //格式二： ['f1', 'f2']
        for (let i = 0; i < filterObject.fields.length; i++) {
            let fName: any = filterObject.fields[i];
            if (typeof fName === 'string' && globalKeys.indexOf(fName) !== -1) {
                queryFields[fName] = true;
            }
        }
    } else {
        queryFields = global;
    }
    if (Object.keys(queryFields).length <= 0) {
        queryFields = global;
    }

    let filter = filterObject;

    if (Object.keys(queryFields).length > 0) {
        filter.fields = queryFields;
        //如果filter.fields既包含true又包含false,则移除false的字段
        if (typeof filter.fields === 'object' && !Array.isArray(filter.fields)) {
            let tag: number = 0;
            let allIsTrueOrFalse: boolean = true;
            const fieldsObj = filter.fields as Record<string, any>;
            let keysOfFields: string[] = Object.keys(fieldsObj);
            for(let index=0; index < keysOfFields.length; index++) {
                let keyOfField: string = keysOfFields[index];
                let valueOfField: any = fieldsObj[keyOfField];
                if (valueOfField === "true" || valueOfField === "1" || valueOfField === true || valueOfField === 1) {
                    tag += 1;
                } else {
                    tag -= 1;
                }
                if (tag != (-1 - index) && tag != (1 + index)) {
                    allIsTrueOrFalse = false;
                    break;
                }
            }
            if (!allIsTrueOrFalse) {
                for(let index=0; index < keysOfFields.length; index++) {
                    let keyOfField: string = keysOfFields[index];
                    let valueOfField: any = fieldsObj[keyOfField];
                    if (!(valueOfField === "true" || valueOfField === "1" || valueOfField === true || valueOfField === 1)) {
                        delete fieldsObj[keyOfField];
                    }
                }
            } else {
                //仅false时，合并全局参数
                if (!fieldsObj[keysOfFields[0]]) {
                    const filterKeys = Object.keys(filter.fields!);
                    for (let index = 0; index < globalKeys.length; index++) {
                        const key = globalKeys[index];
                        if (filterKeys.indexOf(key) === -1) {
                            (filter.fields as any)[key] = true;
                        }
                    }
                }
            }
        }
    } else {
        //兼容旧配置，新版globalFields不会是空的，前端会做验证。
        delete filter.fields;
    }
   return filter;
}

function handleNameMappingAll(data: Array<Record<string, any>>, nameMapping: Record<string, string>) : Array<Record<string, any>> {
    if (!data || !nameMapping) {
        return data;
    }
    let newData : Array<Record<string, any>> = [];
    data.forEach(value => {
        newData.push(applyMapping(value, nameMapping));
    })
    return newData;
}

type JSONValue = string | number | boolean | JSONObject | JSONArray;
interface JSONObject { [key: string]: JSONValue; }
interface JSONArray extends Array<JSONValue> { }

function getDeepValue(obj: any, path: string): any {
    const parts = path.split(".");
    if (parts.length === 1) {
        return obj[parts[0]];
    }
    let current = obj;
    for (const part of parts) {
        if (current == null) return undefined;
        current = current[part];
    }
    return current;
}

function setDeepValue(obj: any, path: string, value: any): void {
    const parts = path.split(".");
    if (parts.length === 1) {
        obj[parts[0]] = value;
        return
    }
    let current = obj;
    for (let i = 0; i < parts.length - 1; i++) {
        const part = parts[i];
        if (
            typeof current[part] !== "object" ||
            Array.isArray(current[part])
        ) {
            current[part] = {};
        }
        current = current[part];
    }
    const last = parts[parts.length - 1];
    current[last] = value;
}

function deepClone(obj: any): any {
    return JSON.parse(JSON.stringify(obj));
}

function deepEach(obj: any) {
    if (obj === 'undefined' || obj === null) {
        return obj;
    }
    // Handle Date objects specifically
    if (obj instanceof Date) {
        return new Date(obj.getTime());
    }
    // Handle arrays before general objects
    if (Array.isArray(obj)) {
       let newArr : [] = [];
       for (let i = 0; i < obj.length; i++) {
           //@ts-ignore
           newArr.push(deepEach(obj[i]));
       }
       return newArr;
    }
    // Handle Map objects specifically
    if (obj instanceof Map) {
        let newMap = new Map();
        obj.forEach((value, key) => {
            newMap.set(key, deepEach(value));
        });
        return newMap;
    }
    // Handle Set objects specifically
    if (obj instanceof Set) {
        let newSet = new Set();
        obj.forEach(value => {
            newSet.add(deepEach(value));
        });
        return newSet;
    }
    // Handle plain objects and Records (Record<K,V> is just a plain object at runtime)
    if (typeof obj === 'object' && obj.constructor === Object) {
        let newObj : Record<string, any> = {};
        Object.keys(obj).forEach(key => {
            newObj[key] = deepEach(obj[key]);
        })
        return newObj;
    }
    // Handle other object types (like custom classes)
    if (typeof obj === 'object') {
        // For custom objects, try to create a new instance of the same constructor
        try {
            let newObj = Object.create(Object.getPrototypeOf(obj));
            Object.keys(obj).forEach(key => {
                newObj[key] = deepEach(obj[key]);
            });
            return newObj;
        } catch (e) {
            return obj;
        }
    }
    return obj;
}

function mergeObjects(target: any, source: any): any {
    if (!target || !source) {
        return target || source || {};
    }
    for (const key in source) {
        if (source.hasOwnProperty(key)) {
            const sourceValue = source[key];

            // Handle null values
            if (sourceValue === null) {
                target[key] = null;
                continue;
            }

            // Handle non-object values (primitives, functions)
            if (typeof sourceValue !== "object") {
                target[key] = sourceValue;
                continue;
            }

            // Handle arrays
            if (Array.isArray(sourceValue)) {
                target[key] = sourceValue;
                continue;
            }

            // Handle Date objects
            if (sourceValue instanceof Date) {
                target[key] = sourceValue;
                continue;
            }

            // Handle Map objects
            if (sourceValue instanceof Map) {
                target[key] = sourceValue;
                continue;
            }

            // Handle Set objects
            if (sourceValue instanceof Set) {
                target[key] = sourceValue;
                continue;
            }

            // Handle plain objects only (not custom class instances)
            if (sourceValue.constructor === Object) {
                if (!target[key] || typeof target[key] !== "object" || Array.isArray(target[key]) || target[key].constructor !== Object) {
                    target[key] = {};
                }
                mergeObjects(target[key], sourceValue);
            } else {
                // For custom objects, just assign directly to preserve their type
                target[key] = sourceValue;
            }
        }
    }
    return target;
}

function applyMapping(input: JSONObject, mapping: Record<string, string>): JSONObject {
    const baseResult = {};
    const untouchedResult = removeMappingKey(deepClone(input), mapping);
    for (const [fromPath, toPath] of Object.entries(mapping)) {
        const value = getDeepValue(input, fromPath);
        if (value === undefined) continue;
        const fromParts = fromPath.split(".");
        const lastKey = fromParts[fromParts.length - 1];
        const parentPath = fromParts.slice(0, -1).join(".");
        const parent = parentPath ? getDeepValue(input, parentPath) : input;
        if (Array.isArray(parent)) {
            const arr: any[] = [];
            for (const item of parent) {
                if (item && typeof item === "object" && lastKey in item) {
                arr.push(item[lastKey]);
                }
            }
            setDeepValue(baseResult, toPath, arr);
        } else {
            setDeepValue(baseResult, toPath, value);
        }
    }
    return mergeObjects(untouchedResult, baseResult);
}

function removeMappingKey(untouchedResult: any, mapping: Record<string, string>) : any {
    Object.keys(mapping).forEach(key => {
        let paths = key.split(".")
        if (paths.length === 1) {
            delete untouchedResult[paths[0]];
        } else {
            let needRemove : any[] = [];
            deepCollect(needRemove, untouchedResult, paths, 0)
            needRemove.forEach(item => {
                delete item[paths[paths.length - 1]];
            })
        }
    })
    return untouchedResult;
}

function deepCollect(collect: any[], map : any, paths: string[], index : number) {
    if (paths.length - 1 >= index) {
        return
    }
    if (typeof map !== 'object') {
        return;
    }
    let path : string = paths[index];
    let value : any = map[path]
    if (Array.isArray(value)) {
        value.forEach(item => {
            deepCollect(collect, item, paths, index + 1);
        })
    } else if (typeof value === 'object') {
        collect.push(value);
        return;
    } else {
        return;
    }
}

function deepReplaceInPlace(jsonObj: Record<string, any>, parameterMap: Record<string, any>) : Record<string, any>{
    if (Array.isArray(jsonObj)) {
        jsonObj.forEach(item => {
            deepReplaceInPlace(item, parameterMap);
        })
    } else if (typeof jsonObj === 'object') {
        Object.keys(jsonObj).forEach(nameKey => {
            let item : any = jsonObj[nameKey] ;
            if (typeof item === 'string') {
                let keys = extractTemplateFields(item)
                if (keys.length == 0) {
                    let patter: string = `\\{\\{${keys[0]}\\}\\}`;
                    let paramValue : any = parameterMap[keys[0]];
                    if (typeof paramValue === 'string' || patter !== item) {
                        jsonObj[nameKey] = item.replace(new RegExp(patter, 'g'), `${paramValue}`);
                    } else {
                        jsonObj[nameKey] = paramValue;
                    }
                } else {
                    let temp = item;
                    keys.forEach(key => {
                        let patter: string = `\\{\\{${key}\\}\\}`;
                        let paramValue : any = parameterMap[key];
                        temp = item.replace(new RegExp(patter, 'g'), `${paramValue}`);
                    })
                    jsonObj[nameKey] = temp;
                }
            } else {
                deepReplaceInPlace(item, parameterMap);
            }
        });
    }
    //log.app.info(`Where: ${JSON.stringify(jsonObj)}`)
    return jsonObj;
}

function extractTemplateFields(input: string): string[] {
    const regex = /\{\{([^{}]+)\}\}/g;
    const matches: string[] = [];
    let match;
    while ((match = regex.exec(input)) !== null) {
        matches.push(match[1]);
    }
    return matches;
}