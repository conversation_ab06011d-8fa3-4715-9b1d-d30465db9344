import {<%= repositoryTypeClass %>, juggler, Options, DataObject} from '@loopback/repository';
import {<%= modelName %>} from '../models';
import {<%= dataSourceClassName %>} from '../datasources';
import {inject} from '@loopback/core';
import {GridFSBucket, ObjectId, Db} from 'mongodb';

export class <%= className %>Repository extends <%= repositoryTypeClass %><
  <%= modelName %>,
  typeof <%= modelName %>.prototype.<%= idProperty %>
> {
  constructor(
    @inject('datasources.<%= dataSourceName %>') dataSource: <%= dataSourceClassName %>,
  ) {
    super(<%= modelName %>, dataSource);
  }

  async create(entity: DataObject<<%= modelName %>>, options?: Options): Promise<<%= modelName %>> {
    let properties = this.modelClass.definition.properties;
    let modelName = this.modelClass.definition.name;
    let autoincrements: string[] = [];
    Object.keys(properties).forEach(field => {
      if (properties[field].autoincrement === true || properties[field].autoincrement === 'true' || properties[field].autoincrement === 'YES' || properties[field].autoincrement === 'yes') {
        autoincrements.push(field);
      }
    });

    if( autoincrements.length > 0) {
      let connector = this.dataSource.connector;
      let db: Db;
      if (connector.connected) {
      db = connector.db
    } else {
      db = await new Promise<Db>((resolve, reject) => {
        connector.connect((err?: any, db?: Db) => {
          if( err ){
            reject(err);
          } else {
          // @ts-ignore
            resolve(db);
          }
        })
      });
    }
    if (db) {
      for( let i = 0; i < autoincrements.length; i++){
        let collection = db.collection('_generatorUniqueId');
        let result = await collection.findOneAndUpdate(
          {_id: `${modelName}.${autoincrements[i]}`},
          {$inc: {seq: 1}},
          {
            // @ts-ignore
            returnOriginal: false,
            upsert: true
          });

          // @ts-ignore
          if( result.value ){
            // @ts-ignore
            entity[autoincrements[i]] = result.value.seq;
          }
        }
      }
    }

    return super.create(entity, options);
  }

  <%_ if( downloadApi ) {%>

  async downloadById(id: string){
    await this.dataSource.connect();
    let connector: any = this.dataSource.connector;
    let db: Db = connector.db;
    let bucket = new GridFSBucket(db, {
      chunkSizeBytes: 255*1024,
      bucketName: '<%= bucketName || 'fs' %>'
    });
    return await bucket.openDownloadStream(new ObjectId(id));
  }
  <%_ }
  if( uploadApi ) {%>


  async upload(file: object) {
    await this.dataSource.connect();
    let connector: any = this.dataSource.connector;
    let db: Db = connector.db;
    let bucketName = this.entityClass.definition.name;
    bucketName = bucketName.substring(0, bucketName.indexOf('.'));
    let bucket = new GridFSBucket(db, {
      chunkSizeBytes: 255*1024,
      bucketName: bucketName
    });
    // @ts-ignore
  let fileName: String = file.originalname.toString();
  // @ts-ignore
  let file_name: String = fileName.substring(0, fileName.lastIndexOf("."));
  // @ts-ignore
  let file_extension: String = fileName.substring(fileName.lastIndexOf(".")+1);
  // @ts-ignore
    let metadata = { size: file.size, contentType: file.mimetype ,file_name: file_name,file_extension: file_extension};
    // @ts-ignore
    let writeStream = bucket.openUploadStream(file.originalname, {
      chunkSizeBytes: 255*1024,
      metadata: metadata
    });
    let fs = require('fs');
    // @ts-ignore
    let readStream = fs.createReadStream(file.path);
    readStream.pipe(writeStream);

    return writeStream.id;
  }

    <%_ }%>

}

